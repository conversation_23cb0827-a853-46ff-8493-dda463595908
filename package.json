{"name": "vue2-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"vite": "^7.0.4"}, "dependencies": {"@vitejs/plugin-vue2": "^2.3.3", "ace-builds": "^1.43.2", "axios": "^1.11.0", "clipboard": "^2.0.11", "element-ui": "^2.15.14", "js-beautify": "^1.15.4", "js-cookie": "^3.0.5", "sass": "^1.89.2", "sass-loader": "^16.0.5", "unplugin-vue-components": "^28.8.0", "vite-plugin-commonjs": "^0.10.4", "vue": "2.7.16", "vue-i18n": "8.28.2", "vue-router": "^3.6.5", "vuedraggable": "^2.24.3", "vuex": "^4.1.0"}}