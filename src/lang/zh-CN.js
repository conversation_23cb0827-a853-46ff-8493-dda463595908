export default {
  application: {
    'zh-CN': '简体中文',
    'en-US': 'English',
  },

  designer: {
    componentLib: '组件库',
    formLib: '表单模板',
    viewLib: '视图组件',
    containerTitle: '容器',
    dragHandlerHint: '鼠标拖拽容器组件或字段组件并放置于表单中',
    dragAction: '拖动',
    basicFieldTitle: '基础字段',
    advancedFieldTitle: '高级字段',
    customFieldTitle: '自定义扩展字段',
    viewFieldTitle: '视图组件',
    viewLayoutTitle: '布局组件',
    viewIntegrateTitle: '集成组件',
    noWidgetHint: '请从左侧列表中选择一个组件, 然后用鼠标拖动组件放置于此处.',

    widgetLabel: {
      grid: '栅格',
      absolute: '绝对定位容器',
      condition: '条件搜索组件',
      'condition-container': '条件搜索容器',
      'condition-container-body': '条件搜索容器主体',
      'condition-container-footer': '条件搜索容器更多',
      iteration: '循环容器',
      whiteboard: '简单容器',
      table: '表格',
      tab: '标签页',
      borad: '写字板',
    transfer: '穿梭框',
      section: '区块',
      'trends-tab': '动态选项卡',
      'organizational-structure': '组织架构',
      'sub-form': '子表单',
      'grid-col': '栅格列',
      'table-cell': '单元格',
      'tab-pane': '选项卡页',
      'data-table': '数据表格',
      'custom-condition': '动态筛选条件',
      data: '数据存储',
      map: '地图',
      barCode: '条码',
      position: '定位组件',
      qrcode: '二维码',
      'custom-tree': '自定义树',
      'vfrom-quote': '引用模板',
      input: '单行输入',
      'order-input': '工单生成',
      'popup-select': '弹窗选择器',
      chart: '图表',
      steps: '步骤条',
      dialog: '弹出层',
      'dialog-body': '弹出体',
      'dialog-footer': '弹出页脚',
      'flow-bus-list': '流程数据表',
      textarea: '多行输入',
      number: '计数器',
      radio: '单选项',

      checkbox: '多选项',
      select: '下拉选项',
      'select-page': '下拉分页选项',
      time: '时间',
      'time-range': '时间范围',
      'date-time': '年月日时分秒时间',
      tree: '下拉树',
      date: '日期',
      'date-range': '日期范围',
      switch: '开关',
      rate: '评分',
      color: '颜色选择器',
      slider: '滑块',
      'static-text': '静态文字',
      'html-text': 'HTML',
      button: '按钮',
      divider: '分隔线',
      codeConfig: '条码配置',
      descriptions:'描述列表',
      userinput: '当前登录人',
      deptinput: '科室',
      dialogTable: '弹框',
      'picture-upload': '图片上传',
      'image': '图片',
      'file-upload': '文件',
      'rich-editor': '富文本',
      cascader: '级联选择',
      carousel: '轮播图',
      timeline: '时间线',
      calendar: '日历',
      export: '导出',
      collapse: '折叠面板',
      drawer: '抽屉',
      'collapse-item': '折叠面板项',
      slot: 'Slot',
      tablePrint: '表格打印',
      onlineComponents: '在线组件',
      webSocket: 'webSocket',
    },

    hint: {
      selectParentWidget: '选中父组件',
      moveUpWidget: '上移组件',
      moveDownWidget: '下移组件',
      cloneWidget: '复制组件',
      insertRow: '插入新行',
      insertColumn: '插入新列',
      remove: '移除组件',
      cellSetting: '单元格操作',
      dragHandler: '拖拽手柄',
      copyField: '复制字段组件',
      onlyFieldWidgetAcceptable: '子表单只能接收字段组件',
      moveUpFirstChildHint: '已经移动到最上面',
      moveDownLastChildHint: '已经移动到最下面',
      fullscreen: '全屏预览',
      cancelfullscreen: '取消全屏',
      closePreview: '关闭',
      copyJson: '复制JSON',
      saveFormJson: '保存为文件',
      copyVueCode: '复制Vue代码',
      copyHtmlCode: '复制HTML代码',
      copyJsonSuccess: '复制JSON成功',
      importJsonSuccess: '导入JSON成功',
      copyJsonFail: '复制JSON失败',
      copyVueCodeSuccess: '复制Vue代码成功',
      copyVueCodeFail: '复制Vue代码失败',
      copyHtmlCodeSuccess: '复制HTML代码成功',
      copyHtmlCodeFail: '复制HTML代码失败',
      saveVueCode: '保存Vue文件',
      saveHtmlCode: '保存Html文件',
      getFormData: '获取数据',
      resetForm: '重置表单',
      disableForm: '禁用编辑',
      enableForm: '恢复编辑',
      exportFormData: '表单数据',
      copyFormData: '复制JSON',
      saveFormData: '保存为文件',
      copyVue2SFC: '复制Vue2代码',
      copyVue3SFC: '复制Vue3代码',
      copySFCFail: '复制SFC代码失败',
      copySFCSuccess: '复制SFC代码成功',
      saveVue2SFC: '保存为Vue2组件',
      saveVue3SFC: '保存为Vue3组件',
      fileNameForSave: '文件名：',
      saveFileTitle: '保存为文件',
      fileNameInputPlaceholder: '请输入文件名',
      sampleLoadedSuccess: '表单示例加载成功',
      sampleLoadedFail: '表单示例加载失败',
      loadFormTemplate: '加载此模板',
      loadFormTemplateHint:
        '是否加载这个模板？加载后会覆盖设计器当前表单，你可以使用“撤销”功能恢复。',
      loadFormTemplateSuccess: '表单模板加载成功',
      loadFormTemplateFailed: '表单模板加载失败',

      widgetSetting: '组件设置',
      formSetting: '表单设置',

      prompt: '提示',
      confirm: '确定',
      document: '文档',
      cancel: '取消',
      import: '导入',
      importJsonHint: '导入的JSON内容须符合下述格式，以保证顺利导入.',
      invalidOptionsData: '无效的选项数据:',
      lastPaneCannotBeDeleted: '仅剩一个选项卡页不可删除.',
      duplicateName: '组件名称已存在: ',
      nameRequired: '组件名称不可为空',

      numberValidator: '数字',
      letterValidator: '字母',
      letterAndNumberValidator: '数字字母',
      mobilePhoneValidator: '手机号码',
      emailValidator: '邮箱',
      urlValidator: '网址',
      noChineseValidator: '非中文字符',
      chineseValidator: '仅中文字符',
      noRepeatValidator: '不可重复出现(在容器中)',

      rowspanNotConsistentForMergeEntireRow:
        '存在行高不一致的单元格, 无法合并整行.',
      colspanNotConsistentForMergeEntireColumn:
        '存在列宽不一致的单元格, 无法合并整列.',
      rowspanNotConsistentForDeleteEntireRow:
        '存在行高不一致的单元格, 不可删除整行.',
      colspanNotConsistentForDeleteEntireColumn:
        '存在列宽不一致的单元格, 不可删除整列.',
    },

    toolbar: {
      undoHint: '撤销',
      redoHint: '重做',
      pcLayout: 'PC',
      padLayout: 'Pad',
      mobileLayout: 'H5',
      clear: '清空',
      preview: '预览',
      importJson: '导入',
      exportJson: '导出',
      exportCode: '导出代码',
      generateCode: '生成代码',
      generateSFC: '生成SFC',
      generateSave: '保存',
      exit: '退出',
      nodeTreeHint: '组件层次结构树',
      nodeTreeTitle: '组件层次结构树',
    },

    setting: {
      flowType: '流程类型',
      basicSetting: '基本属性',
      attributeSetting: '属性设置',
      commonSetting: '常见属性',
      advancedSetting: '高级属性',
      eventSetting: '事件属性',
      interfaceSetting: '接口属性',
      uniqueName: '唯一名称',
      label: '字段标签',
      busType: '业务类型',
      indexes: '索引类型',
      uniqueIndex: '唯一索引',
      normalIndex: '普通索引',
      displayType: '显示类型',
      defaultValue: '默认值',
      popupSelect: '参数配置',
      placeholder: '占位内容',
      emptyText: '内容为空占位符',
      iconClass: '树节点的图标',
      startPlaceholder: '起始占位内容',
      endPlaceholder: '截止占位内容',
      widgetColumnWidth: '组件列宽',
      widgetSize: '组件大小',
      showStops: '显示间断点',
      displayStyle: '显示样式',
      inlineLayout: '行内',
      blockLayout: '块',
      buttonStyle: '显示为按钮',
      border: '带有边框',
      labelWidth: '标签宽度',
      rows: '行数',
      labelHidden: '隐藏字段标签',
      required: '必填字段',
      addAble: '新增',
      deleteAble: '删除',
      rowEdit: '只读可编辑行',
      validation: '字段校验',
      validationHelp: '支持输入正则表达式',
      validationHint: '校验失败提示',
      readonly: '只读',
      disabled: '禁用',
      hidden: '隐藏',
      tableShow: '数据表格是否显示',
      operation: '是否显示操作列',
      samePage: '子表单同页显示',
      groupPage: '子表单分组显示',
      dialogTable: '弹窗类型',
      textContent: '静态文字',
      htmlContent: 'HTML',
      clearable: '可清除',
      editable: '可输入',
      format: '显示格式',
      valueFormat: '绑定值格式',
      showPassword: '可显示密码',
      filterable: '可搜索选项',
      allowCreate: '允许创建选项',
      remote: '可远程搜索',
      automaticDropdown: '自动弹出选项',
      multiple: '选项可多选',
      showNodeType: '显示节点类型',
      multipleLimit: '多选数量限制',
      contentPosition: '文字位置',
      plain: '朴素按钮',
      round: '圆角按钮',
      circle: '圆形按钮',
      icon: '图标',
      optionsSetting: '选项设置',
      addOption: '增加选项',
      importOptions: '导入选项',
      resetDefault: '重设选中项',
      uploadSetting: '上传参数设置',
      uploadURL: '上传地址',
      uploadTip: '上传提示内容',
      withCredentials: '发送cookie凭证',
      multipleSelect: '文件可多选',
      showFileList: '显示文件列表',
      limit: '最大上传数量',
      fileMaxSize: '文件大小限制(MB)',
      fileTypes: '上传文件类型',
      fileTypesHelp: '支持添加其他文件类型',
      headers: '上传请求头',

      cellWidth: '宽度',
      cellHeight: '高度',
      gutter: '栅格间隔(像素)',
      columnSetting: '栅格属性设置',
      colsOfGrid: '当前栅格列:',
      colSpanTitle: '栅格宽度',
      colOffsetTitle: '左侧间隔格数',
      colPushTitle: '右移栅格数',
      colPullTitle: '左移栅格数',
      addColumn: '增加栅格',
      responsive: '响应式布局',

      tabPaneSetting: '选项卡设置',
      addTabPane: '增加选项卡页',
      paneActive: '激活',

      customLabelIcon: '定制字段标签',
      labelIconClass: '标签Icon样式',
      labelIconPosition: '标签Icon位置',
      labelTooltip: '标签文字提示',
      minValue: '最小值',
      maxValue: '最大值',
      precision: '精度',
      step: '增减步长',
      controlsPosition: '控制按钮位置',
      minLength: '最小长度',
      maxLength: '最大长度',
      showWordLimit: '显示字数统计',
      prefixIcon: '头部Icon',
      suffixIcon: '尾部Icon',
      inputButton: '输入框按钮设置',
      appendButton: '添加后置按钮',
      appendButtonDisabled: '后置按钮禁用',
      appendButtonIcon: '按钮Icon',
      buttonIcon: '按钮Icon',
      switchWidth: '开关宽度（像素）',
      activeText: '开启时文字描述',
      inactiveText: '关闭时文字描述',
      activeColor: '开启时背景色',
      inactiveColor: '关闭时背景色',
      maxStars: '最大评分值',
      lowThreshold: '低分界限值',
      highThreshold: '高分界限值',
      allowHalf: '允许半选',
      showText: '显示辅助文字',
      showScore: '显示当前分数',
      range: '是否为范围选择',
      vertical: '是否竖向显示',
      showBlankRow: '默认显示新行',
      showRowNumber: '显示行号',

      insertColumnToLeft: '插入左侧列',
      insertColumnToRight: '插入右侧列',
      insertRowAbove: '插入上方行',
      insertRowBelow: '插入下方行',
      mergeLeftColumn: '合并左侧单元格',
      mergeRightColumn: '合并右侧单元格',
      mergeEntireRow: '合并整行',
      mergeRowAbove: '合并上方单元格',
      mergeRowBelow: '合并下方单元格',
      mergeEntireColumn: '合并整列',
      undoMergeCol: '撤销列合并',
      undoMergeRow: '撤销行合并',
      deleteEntireCol: '删除整列',
      deleteEntireRow: '删除整行',

      widgetName: '组件唯一名称',
      formSize: '全局组件大小',
      labelPosition: '字段标签位置',
      topPosition: '顶部',
      leftPosition: '左边',
      labelAlign: '字段标签对齐',
      leftAlign: '居左',
      centerAlign: '居中',
      rightAlign: '居右',
      formCss: '表单全局CSS',
      addCss: '编写CSS',
      customClass: '自定义CSS样式',
      globalFunctions: '表单全局函数',
      addEventHandler: '编写代码',
      editWidgetEventHandler: '组件事件处理',
      editFormEventHandler: '表单事件处理',
      formSFCSetting: '生成SFC设置',
      formModelName: '数据对象名称',
      formRefName: '引用名称',
      formRulesName: '验证规则名称',
      api: '数据来源',
      organizationalList: '挂载节点',
      bandDataIdsApi: '回显api',
      defaultTime: '默认时间',
      paramConfig: '参数配置',
      btns: '操作按钮',
      bandKey: '字段名',
      borderRadius: '圆角设置',
      backgroundColor: '背景颜色',
      isUser: '是否为人员选择',
      autoRefresh: '自动更新',
      defaultAhead: '默认往前推',
      isEnable: '表格拖拽排序',
      reportRelevance: '报表关联接口',
      isShowExport: '是否显示导出',
      isShowPrint: '是否显示打印',
      isShowQuery: '是否显示查询',
      onlineRelevance: '在线组件关联接口',
    },
  },
};
