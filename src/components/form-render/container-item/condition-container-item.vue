<template>
  <div :key="widget.id" class="d-flex flex-wrap">
    <div class="container pd-r10" style="flex: 1">
      <template v-for="(subWidget, swIdx) in widget.widgetList[0].widgetList">
        <template v-if="'container' === subWidget.category">
          <component
            :is="subWidget.type + '-item'"
            :widget="subWidget"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
        <template v-else>
          <component
            :is="subWidget.type + '-widget'"
            :field="subWidget"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
      </template>
      <el-collapse-transition>
        <div v-show="moreFlag" style="margin-top: 5px">
          <template
            v-for="(subWidget, swIdx) in widget.widgetList[1].widgetList"
          >
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-item'"
                :widget="subWidget"
                :key="swIdx"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :designer="null"
                :key="swIdx"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
          </template>
        </div>
      </el-collapse-transition>
    </div>
    <!-- :style="{ 'width': widget.widgetList[1].widgetList.length > 0 ? '245px' : '220px' }" -->
    <div>
      <el-button
        type="primary"
        icon="el-icon-search"
        class="mr-l5"
        @click="query"
        >查询</el-button
      >
      <el-button class="mr-l10" icon="el-icon-refresh-left" plain @click="clear"
        >重置</el-button
      >
      <span
        class="el-dropdown-link"
        @click="more"
        v-if="widget.widgetList[1].widgetList.length > 0"
      >
        {{ !moreFlag ? '展开' : '收缩'
        }}<i
          class="el-icon-arrow-down"
          :style="{ transform: moreFlag ? 'rotate(180deg)' : 'rotate(0)' }"
        ></i>
      </span>
    </div>
  </div>
</template>

<script>
import { executeInterface } from '@/api/interfaces/interfaces';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import bus from '@/magic-editor/scripts/bus';
import refMixin from '@/components/form-render/refMixin';

import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
export default {
  name: 'condition-container-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  computed: {
    items() {
      return this.widget.widgetList;
    },
  },
  data() {
    return {
      screenList: [],
      conditions: {
        elementKey: '', // 选中的项目key
        elementType: '', // 选中的项目类型
        value: undefined, // 选中的项目对应输入的值
        relation: 'or', // 关系(或/且)
        options: [], // select的选项
        bandApi: '', // 下拉分页的请求
        condition: '',
        elementJson: {},
      },
      moreFlag: false,
    };
  },
  mounted() {
    this.screenList = [[{ ...this.conditions }]];
    // 绑定enter事件
    this.enterKeyup();
  },
  destroyed() {
    // 销毁enter事件
    this.enterKeyupDestroyed();
  },
  methods: {
    enterKey(event) {
      const code = event.keyCode
        ? event.keyCode
        : event.which
        ? event.which
        : event.charCode;
      if (code == 13) {
        this.query();
      }
    },
    enterKeyupDestroyed() {
      document.removeEventListener('keyup', this.enterKey);
    },
    enterKeyup() {
      document.addEventListener('keyup', this.enterKey);
    },
    more() {
      this.moreFlag = !this.moreFlag;
      setTimeout(() => {
        bus.$emit('resizeHeight');
      }, 500);
    },

    query() {
      if (this.widget.options.onSearchClick) {
        let JS = new Function(this.widget.options.onSearchClick);
        JS.call(this);
      }
    },
    /**
     * 重置
     */
    clear() {
      let arr = this.widget.widgetList[0].widgetList;
      let that = this;

      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              that.getWidgetRef(item.id).resetField();
            }

            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
      if (this.widget.options.onResetClick) {
        let JS = new Function(this.widget.options.onResetClick);
        JS.call(this);
      }
    },


  },
};
</script>
<style lang="scss" scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  font-size: 12px;
  margin-left: 10px;
}

.el-icon-arrow-down {
  font-size: 12px;
  transition: 0.3s;
}
</style>
