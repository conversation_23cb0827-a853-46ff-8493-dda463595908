<template>
  <el-col
    class="grid-cell"
    :class="[customClass]"
    v-bind="layoutProps"
    :key="widget.id"
    v-show="!widget.options.hidden"
    v-if="widget.options.authority ? checkPermi([widget.options.name]) : true"
    :style="{
      margin: marginComp,
      padding: paddingComp,
      border: `${
        (widget.options.border2 ? widget.options.border2.width || 0 : 0) + 'px'
      } ${widget.options.border2 ? widget.options.border2.type : 'none'} ${
        widget.options.border2 ? widget.options.border2.color || '#000' : ''
      }`,
    }"
  >
    <template v-if="!!widget.widgetList && widget.widgetList.length > 0">
      <template v-for="(subWidget, swIdx) in widget.widgetList">
        <template v-if="'container' === subWidget.category">
          <component
            :is="subWidget.type + '-item'"
            :widget="subWidget"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
        <template v-else-if="tabIndex === null">
          <component
            :is="subWidget.type + '-widget'"
            :field="subWidget"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
        <template v-else>
          <component
            :is="subWidget.type + '-widget'"
            :field="subWidget"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="tabWidget"
            :sub-form-row-id="tabIndex + ''"
            :sub-form-row-index="tabIndex"
          ></component>
        </template>
      </template>
    </template>
    <template v-else>
      <el-col>
        <div class="blank-cell">
          <span class="invisible-content">{{
            i18nt('render.hint.blankCellContent')
          }}</span>
        </div>
      </el-col>
    </template>
  </el-col>
</template>

<script>
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';

export default {
  name: 'GridColItem',
  componentName: 'ContainerItem',
  mixins: [i18n, refMixin, containerItemMixin],
  components: {
    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    tabIndex: null,
    tablFildId: null,
    tabWidget: null,
  },
  inject: ['refList', 'globalModel', 'formConfig', 'previewState'],
  data() {
    return {
      layoutProps: {
        span: this.widget.options.span,
        md: this.widget.options.md || 12,
        sm: this.widget.options.sm || 12,
        xs: this.widget.options.xs || 12,
        offset: this.widget.options.offset || 0,
        push: this.widget.options.push || 0,
        pull: this.widget.options.pull || 0,
      },
    };
  },
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
    marginComp() {
      const { margin } = this.widget.options;
      if (margin) {
        return `${margin.mt || 0}px ${margin.mr || 0}px ${margin.mb || 0}px ${
          margin.ml || 0
        }px `;
      }
      return 0;
    },
    paddingComp() {
      const { padding } = this.widget.options;
      if (padding) {
        return `${padding.pt || 0}px ${padding.pr || 0}px ${
          padding.pb || 0
        }px ${padding.pl || 0}px `;
      }
      return 0;
    },
  },
  created() {
    this.initLayoutProps();
    this.initRefList();
  },
  methods: {
    initLayoutProps() {
      if (!!this.widget.options.responsive) {
        if (!!this.previewState) {
          this.layoutProps.md = undefined;
          this.layoutProps.sm = undefined;
          this.layoutProps.xs = undefined;

          let lyType = this.formConfig.layoutType;
          if (lyType === 'H5') {
            this.layoutProps.span = this.widget.options.xs || 12;
          } else if (lyType === 'Pad') {
            this.layoutProps.span = this.widget.options.sm || 12;
          } else {
            this.layoutProps.span = this.widget.options.md || 12;
          }
        } else {
          this.layoutProps.span = undefined;
        }
      } else {
        this.layoutProps.md = undefined;
        this.layoutProps.sm = undefined;
        this.layoutProps.xs = undefined;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.blank-cell {
  font-style: italic;
  color: #cccccc;

  span.invisible-content {
    opacity: 0;
  }
}
</style>
