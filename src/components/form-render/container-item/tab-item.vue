<template>
  <container-item-wrapper :widget="widget">
    <div :key="widget.id" class="tab-container" v-show="!widget.options.hidden">
      <el-tabs
        v-model="activeTabName"
        :type="widget.options.tabType || ''"
        :tab-position="widget.options.tabPosition"
        :stretch="widget.options.stretch"
        :ref="widget.id"
        :class="[customClass]"
      >
        <el-tab-pane
          v-for="(tab, index) in visibleTabs"
          :key="index"
          :disabled="tab.options.disabled"
          :name="tab.options.label"
        >
          <span slot="label">
            <svg-icon :icon-class="tab.options.icon" v-if="tab.options.icon" />
            {{ tab.options.label }}</span
          >
          <template v-for="(subWidget, swIdx) in tab.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-item'"
                :widget="subWidget"
                :key="swIdx"
                :parent-list="tab.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :key="swIdx"
                :parent-list="tab.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import containerItemMixin from './containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import bus from '@/magic-editor/scripts/bus';

export default {
  name: 'tab-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  data() {
    return {
      activeTabName: '',
    };
  },
  computed: {
    visibleTabs() {
      return this.widget.tabs.filter((tp) => {
        return !tp.options.hidden;
      });
    },
  },
  created() {
    this.initRefList();
  },
  mounted() {
    this.activeTabName = this.visibleTabs[0].options.label;
  },
  watch: {
    activeTabName(oldValue, newValue) {
      //
      if (this.widget.options.tabChange) {
        this.$nextTick(() => {
          let JS = new Function(
            'newValue',
            'oldValue',
            this.widget.options.tabChange,
          );
          JS.call(this, oldValue, newValue);
        });
      }
    },
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    getValue() {
      return this.activeTabName;
    },
    setValue(val) {
      this.activeTabName = val;
    },
    getCurrentTab() {
      return this.visibleTabs.find(
        (item) => item.options.label == this.activeTabName,
      );
    },
  },
};
</script>

<style lang="scss" scoped></style>
