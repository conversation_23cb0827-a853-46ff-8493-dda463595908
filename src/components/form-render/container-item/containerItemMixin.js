import { generateId } from '@/utils/util';
import request from '@/utils/request';
import { getToken } from '@/utils/auth';

export default {
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },

    formModel: {
      cache: false,
      get() {
        return this.globalModel.formModel;
      },
    },
  },

  methods: {
    waitHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
        async: false,
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';

      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings).responseJSON;
    },
    asyncHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';
      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings);
    },
    getRequest(pp) {
      return request(pp);
    },
    unregisterFromRefList() {
      //销毁容器组件时注销组件ref
      if (this.refList !== null && !!this.widget.options.name) {
        let oldRefName = this.widget.options.name;
        delete this.refList[oldRefName];
      }
    },

    //--------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */

    setHidden(flag) {
      this.widget.options.hidden = flag;
    },

    activeTab(tabIndex) {
      //tabIndex从0计数
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs.forEach((tp, idx) => {
          tp.options.active = idx === tabIndex;
          if (idx === tabIndex) {
            this.activeTabName = tp.options.name;
          }
        });
      }
    },

    disableTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.disabled = true;
      }
    },

    enableTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.disabled = false;
      }
    },

    hideTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.hidden = true;
      }
    },

    showTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.hidden = false;
      }
    },

    disableSubFormRow(rowIndex) {
      this.widget.widgetList.forEach((subWidget) => {
        let swRefName =
          subWidget.options.name + '@row' + this.rowIdData[rowIndex];
        let foundSW = this.getWidgetRef(swRefName);
        if (!!foundSW) {
          foundSW.setDisabled(true);
        }
      });
    },

    enableSubFormRow(rowIndex) {
      this.widget.widgetList.forEach((subWidget) => {
        let swRefName =
          subWidget.options.name + '@row' + this.rowIdData[rowIndex];
        let foundSW = this.getWidgetRef(swRefName);
        if (!!foundSW) {
          foundSW.setDisabled(false);
        }
      });
    },

    disableSubForm() {
      if (this.rowIdData.length > 0) {
        this.rowIdData.forEach((dataRow, rIdx) => {
          this.disableSubFormRow(rIdx);
        });
      }
    },

    enableSubForm() {
      if (this.rowIdData.length > 0) {
        this.rowIdData.forEach((dataRow, rIdx) => {
          this.enableSubFormRow(rIdx);
        });
      }
    },

    resetSubForm() {
      //重置subForm数据为空
      if (this.widget.type === 'sub-form') {
        let subFormModel = this.formModel[this.widget.options.name];
        if (!!subFormModel) {
          subFormModel.splice(0, subFormModel.length);
          this.rowIdData.splice(0, this.rowIdData.length);
        }
      }
    },

    setLabel(newLabel) {
      this.widget.options.label = newLabel;
    },
    getSubFormValues(needValidation = true) {
      if (this.widget.type === 'sub-form') {
        //TODO: 逐行校验子表单！！
        return this.formModel[this.widget.options.name];
      } else {
        this.$message.error(this.i18nt('render.hint.nonSubFormType'));
      }
    },
    handleOnMounted() {
      if (this.widget.options.onMounted) {
        let mountFunc = new Function(this.widget.options.onMounted);
        mountFunc.call(this);
      }
    },
    handleOnCreated() {
      if (this.widget.options.onCreated) {
        let customFunc = new Function(this.widget.options.onCreated);
        customFunc.call(this);
      }
    },
    // validateField(fieldName) { //逐行校验子表单字段
    //   //TODO:
    // },
    //
    // validateSubForm() { //逐行校验子表单全部字段
    //   //TODO:
    // },

    //--------------------- 以上为组件支持外部调用的API方法 end ------------------//
    setHasRole(flag) {
      this.widget.options.hasRole = flag;
    },
    getSonWidgetRef(id) {
      let ref = null;
      const getDataSource = (data) => {
        if (data?.widgetList && data?.widgetList.length) {
          return data?.widgetList;
        }
        if (data?.cols && data?.cols.length) {
          return data?.cols;
        }
        if (data?.tabs && data?.tabs.length) {
          return data?.tabs;
        }

        return [];
      };
      const bfs = (root) => {
        if (root.id === id) {
          ref = this.getWidgetRef(id);
        }
        // console.log(root.id);
        getDataSource(root).forEach((item) => bfs(item));
      };
      bfs(this.widget);
      return ref;
    },
    /**
     * 批量设置栅格下所有节点的属性
     * @param {*} nodeId string  节点id
     * @param {*} config object  option配置项
     */
    setGridAllNode(config) {
      // 1.先找到目标节点
      let targetNode =
        this.widget?.widgetList || this.widget?.cols || this.widget?.tabs;

      if (targetNode && targetNode.length) {
        this.$array.handleWidgetList(targetNode, (item) => {
          for (let key in config) {
            if (key in item.options) {
              item.options[key] = config[key];
            }
          }
        });
      } else {
        this.$message.error(`节点${this.widget.id}下未发现有子元素`);
      }
    },
  },
};
