<template>
  <container-item-wrapper :widget="widget">
    <el-card
      :key="widget.id"
      class="card-container"
      :class="[!!widget.options.folded ? 'folded' : '', customClass]"
      :shadow="widget.options.shadow"
      :style="{ width: widget.options.cardWidth + '!important' || '' }"
      :ref="widget.id"
      v-show="!widget.options.hidden"
    >
      <div slot="header" class="clear-fix">
        <span
          style="
            font-size: 16px;
            color: rgb(18, 19, 21);
            font-weight: 600;
            background-color: inherit;
          "
          >{{ widget.options.label }}</span
        >
        <i
          v-if="widget.options.showFold"
          class="float-right el-icon-arrow-down toggle"
          :style="{transform:widget.options.folded?'rotate(-180deg)':''}"
          @click="toggleCard"
        ></i>
        <span
          style="
            font-size: 12px;
            color: #999;
            background-color: inherit;
            display: block;
            margin-top: 5px;
          "
          v-if="widget.options.subtitle"
          >{{ widget.options.subtitle }}</span
        >
      </div>
      <template v-if="!!widget.widgetList && widget.widgetList.length > 0">
        <template v-for="(subWidget, swIdx) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component
              :is="subWidget.type + '-item'"
              :widget="subWidget"
              :key="swIdx"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
            ></component>
          </template>
          <template v-else>
            <component
              :is="subWidget.type + '-widget'"
              :field="subWidget"
              :designer="null"
              :key="swIdx"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
            ></component>
          </template>
        </template>
      </template>
    </el-card>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'card-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, fieldMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  created() {
    this.initRefList();
  },
  mounted() {
    //
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    toggleCard() {
      this.widget.options.folded = !this.widget.options.folded;
    },
    toggleState(bool) {
      if (!this.widget.options.showFold) {
        this.$message.error('请开启卡片组件的是否收缩配置');
        return;
      }
      this.widget.options.folded = !bool;
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container{
  box-sizing: border-box;
}

::v-deep .el-card__header {
  padding: 10px 12px;
}
::v-deep .el-card__body {
  max-height: 100vh;
  transition: all .5s;
  padding: 20px;
  overflow: hidden;
}
.folded ::v-deep .el-card__body {
   max-height: 0;
   padding: 0;
}
.toggle{
  cursor: pointer;
  transition: all .5s;
}

.clear-fix:before,
.clear-fix:after {
  display: table;
  content: '';
}

.clear-fix:after {
  clear: both;
}

.float-right {
  float: right;
}

</style>
