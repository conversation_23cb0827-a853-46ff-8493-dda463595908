<template>
  <container-item-wrapper :widget="widget">
    <el-row
      :key="widget.id"
      :gutter="widget.options.gutter"
      class="grid-container"
      :class="[customClass]"
      :ref="widget.id"
      v-show="!widget.options.hidden"
      :style="{
        margin: marginComp,
        padding: paddingComp,
        border: `${
          (widget.options.border2 ? widget.options.border2.width || 0 : 0) +
          'px'
        } ${widget.options.border2 ? widget.options.border2.type : 'none'} ${
          widget.options.border2 ? widget.options.border2.color || '#000' : ''
        }`,
      }"
    >
      <template v-for="(colWidget, colIdx) in widget.cols">
        <grid-col-item
          :widget="colWidget"
          :key="colIdx"
          :parent-list="widget.cols"
          :index-of-parent-list="colIdx"
          :parent-widget="widget"
          :tabIndex="tabIndex"
          :tablFildId="tablFildId"
          :tabWidget="tabWidget"
        ></grid-col-item>
      </template>
    </el-row>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import GridColItem from './grid-col-item';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'grid-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    GridColItem,
  },
  computed: {
    marginComp() {
      const { margin } = this.widget.options;
      if (margin) {
        return `${margin.mt || 0}px ${margin.mr || 0}px ${margin.mb || 0}px ${
          margin.ml || 0
        }px `;
      }
      return 0;
    },
    paddingComp() {
      const { padding } = this.widget.options;
      if (padding) {
        return `${padding.pt || 0}px ${padding.pr || 0}px ${
          padding.pb || 0
        }px ${padding.pl || 0}px `;
      }
      return 0;
    },
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
    tabWidget: null,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  mounted() {
    // console.log(this.widget.options)
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {

    clear() {
      let arr = this.widget.cols;
      let that=this

      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (widget != null && widget.field && widget.field.category != 'category') {
              that.getWidgetRef(item.id).resetField();
            }

            if (item.cols && item.cols.length > 0) {
              cr(item.cols)
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList)
            }
          } catch (e){
            // 里面可能有栅格等字段没有清除接口
          }
        });
      }
      //迭代
      cr(arr)
    },
    validate() {
      return new Promise((resolve, reject) => {
        let arr = this.widget.cols;
        let that=this
        let formData = {}
        let cr = (arr) => {
          arr.forEach((item) => {
            try {
              let widget = that.getWidgetRef(item.id);
              if (widget != null && widget.field && widget.field.category != 'category') {
                let errorMessage = widget.validate();
                let validateFlag =  errorMessage == '' || !errorMessage ? true : false;
                if (!validateFlag) {
                  reject({field: widget, errorMessage: errorMessage})
                } else {
                  // 如果有别名 设置别名
                  if (widget.field.options.alias) {
                    formData[widget.field.options.alias] = widget.getValue()
                  } else {
                    formData[widget.field.options.name] = widget.getValue()
                  }
                }
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols)
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList)
              }
            } catch (e){
              // 里面可能有栅格等字段没有清除接口
              reject({field: null, errorMessage: '校验错误'})
            }
          });
        }
        //迭代
        cr(arr)
        resolve(formData);
      });
    },
    clearValidate() {
      this.getFormRef().$refs.renderForm.clearValidate()
    },
    setCategoryValue(data, flag = true) {
      let arr = this.widget.cols;
      let that=this

      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (widget != null && widget.field && widget.field.category != 'category') {
              try {
                let value = data[widget.field.options.name] || data[widget.field.options.alias]
                // 空值也设置
                if (flag && (!value || value == '')) {
                  if (item.type == 'data-table') {
                    widget.updateTableData([]);
                  } else {
                    widget.resetField()
                  }
                } else {
                  if (item.type == 'data-table') {
                    if (!value || value !== '') {
                      if (typeof value == 'string') {
                        // 需要转json
                        widget.updateTableData(JSON.parse(value));
                      } else {
                        widget.updateTableData(value);
                      }
                    }
                  } else {
                    if (!value || value !== '') {
                      widget.setValue(value);
                    }
                  }
                }
              } catch (e) {
                console.error(widget.field.options.label+"设置错误" , e)
              }
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols)
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList)
            }
          } catch (e){
            // 里面可能有栅格等字段没有清除接口
          }
        });
      }
      //迭代
      cr(arr)
    },

  },
};
</script>

<style lang="scss" scoped></style>
