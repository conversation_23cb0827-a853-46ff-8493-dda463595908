<template>
  <el-collapse-item  :class="[customClass]" :title="widget.options.title"
          :key="widget.id" v-show="!widget.options.hidden">
      <template v-for="(subWidget, swIdx) in widget.widgetList">
        <template v-if="'container' === subWidget.category">
          <component :is="subWidget.type + '-item'" :widget="subWidget" :key="swIdx" :parent-list="widget.widgetList"
                          :index-of-parent-list="swIdx" :parent-widget="widget"></component>
        </template>
        <template v-else>
          <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="null" :key="swIdx" :parent-list="widget.widgetList"
                        :index-of-parent-list="swIdx" :parent-widget="widget"></component>
        </template>
      </template>
  </el-collapse-item>
</template>

<script>
  import i18n from "../../../utils/i18n"
  import refMixin from "../refMixin"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "CollapseItemItem",
    componentName: 'ContainerItem',
    mixins: [i18n, refMixin],
    components: {
      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      title:String
    },
    inject: ['refList', 'globalModel', 'formConfig', 'previewState'],
    data() {
      return {
      }
    },
    computed: {
      customClass() {
        return this.widget.options.customClass || ''
      }
    },
    created() {
      this.initRefList()
    },
    methods: {

    }
  }
</script>

<style lang="scss" scoped>
::v-deep .el-collapse-item__content{
  padding:0 2px 25px;
  overflow: hidden;
}
</style>
