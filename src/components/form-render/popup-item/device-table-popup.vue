<template>
  <el-dialog
    :title="title"
    :modal="false"
    :before-close="handleClose"
    :visible.sync="dialogVisible">
    <el-table
      ref="singleTable"
      v-loading="loading"
      :data="tableData"
      highlight-current-row
      @current-change="handleCurrentChange"
      style="width: 100%">
      <el-table-column
        prop="device_id"
        label="设备Id"
        width="180">
      </el-table-column>
      <el-table-column
        prop="db_device_name"
        label="设备名称"
        width="180">
      </el-table-column>
      <el-table-column
        prop="db_device_code"
        label="设备编码">
      </el-table-column>
      <el-table-column
        prop="db_device_model"
        label="设备型号">
      </el-table-column>
    </el-table>
    <lt-pagination
      :total="total"
      :page.sync="current"
      :limit.sync="size"
      :auto="false"
      @pagination="getData"
    />
  </el-dialog>
</template>

<script>
import { executeInterface } from '@/api/interfaces/interfaces'
export default {
  name: "device-table-popup",
  props: {
    title: {
      type: String,
      default: '设备选择'
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    fieldData: {
      type: Object,
      default: null
    }
  },
  data (){
    return {
      dialogVisible: this.dialogVisible,
      current: 1, // 当前页码
      size: 10, // 每页的数量
      loading: false,
      tableData: [],
      total: 0
    }
  },
  mounted() {
    this.getData()
  },
  methods:{
    handleClose(done) {
      this.$emit('popupClose')
    },
    handleCurrentChange(val) {
      this.fieldData.fieldModel = val.device_id + '';
      this.fieldData.fieldName =  val.db_device_name;
      this.fieldData.popupData = val;

    },
    getData() {
      this.loading = true
      let data = {
        pageNum: this.current,
        pageSize: this.size
      }
      executeInterface({
        apiId: 'cfd12179723a4f7d8739c014ad37c36d',
        body: data
      }).then(e => {
        this.tableData = e.data.records
        this.total = e.data.total
        this.loading = false
        if (this.fieldData.fieldModel != null) {
          this.$refs.singleTable.setCurrentRow(e.data.records.filter(item => item.device_id == this.fieldData.fieldModel)[0]);
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
