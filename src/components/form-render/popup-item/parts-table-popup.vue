<template>
  <el-dialog
    :title="title"
    :modal="false"
    :visible.sync="dialogVisible">
    <span>备品备件</span>
  </el-dialog>
</template>

<script>
export default {
  name: "parts-table-popup",
  props: {
    title: {
      type: String,
      default: '弹出层'
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
  },
  data (){
    return {

    }
  }
}
</script>

<style scoped>

</style>
