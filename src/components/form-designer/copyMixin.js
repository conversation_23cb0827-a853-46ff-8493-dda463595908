const dbKey = 'widgetJSON';
import bus from '@/scripts/bus';
export default {
  mounted() {
    bus.$on('shortcutKeys', this.handleEvent);
  },
  computed: {
    getWidget: {
      get() {
        return this?.widget || this?.field;
      },
      set() {},
    },
    isSelect() {
      if (this.designer && this.getWidget) {
        return (
          this.designer.selectedId === this.getWidget.id ||
          this.designer.selectedId === this.getWidget.id
        );
      }
      return false;
    },
  },
  methods: {
    handleEvent(event) {
      if (this.isSelect) {
        // 复制
        if ((event.ctrlKey || event.metaKey) && event.keyCode == 67) {
          console.log('开始复制');
          localStorage.setItem(dbKey, JSON.stringify(this.getWidget));
        }
        // 粘贴
        if ((event.ctrlKey || event.metaKey) && event.keyCode == 86) {
          console.log('开始粘贴');
          let widgetJSON = localStorage.getItem(dbKey);
          if (!widgetJSON) {
            return;
          }
          widgetJSON = JSON.parse(widgetJSON);

          const { type, id } = this.getWidget;

          // 限制粘贴的目标元素
          if (
            !id.includes('grid') &&
            !id.includes('card') &&
            !id.includes('tab')
          ) {
            return;
          }

          // card,tab 复制与粘贴不能一致
          if (
            (id.includes('card') || id.includes('tab')) &&
            widgetJSON.id === id
          ) {
            return;
          }

          // 复制节点与粘贴节点必须同时为栅格
          if (type === 'grid' && widgetJSON.type !== 'grid') {
            return;
          }

          // 对id进行替换处理
          widgetJSON = {
            widgetList: [widgetJSON],
          };
          widgetJSON = JSON.parse(this.$string.dataTemplateReplace(widgetJSON));
          widgetJSON = widgetJSON.widgetList[0];
          // 栅格 -> 栅格  替换其中的cols
          if (widgetJSON.type === 'grid' && type === 'grid') {
            this.getWidget.cols = widgetJSON.cols;
            localStorage.removeItem(dbKey);
            return;
          }
          // 栅格列 -> 栅格列 整体替换 其中的内容
          if (widgetJSON.type === 'grid-col' && type === 'grid-col') {
            this.getWidget.widgetList = widgetJSON.widgetList;
            localStorage.removeItem(dbKey);
            return;
          }

          if (id.includes('tab')) {
            this.getWidget.tabs.forEach((e) => {
              if (e.options.active) {
                e.widgetList.push(widgetJSON);
                localStorage.removeItem(dbKey);
                return;
              }
            });
          }
          let arr = ['widgetList', 'cols'];
          // 执行向后追加
          for (let i = 0; i < arr.length; i++) {
            if (arr[i] in this.getWidget) {
              this.getWidget[arr[i]].push(widgetJSON);
              localStorage.removeItem(dbKey);
              return;
            }
          }
        }
      }
    },
    selectParentWidget() {
      if (this.parentWidget) {
        this.designer.setSelected(this.parentWidget);
      }
    },

    selectBroWidget(type) {
      if (type == 'last' && this.parentList[this.indexOfParentList - 1]) {
        this.designer.setSelected(this.parentList[this.indexOfParentList - 1]);
      } else if (
        type == 'next' &&
        this.parentList[this.indexOfParentList + 1]
      ) {
        this.designer.setSelected(this.parentList[this.indexOfParentList + 1]);
      }
    },
  },
};
