<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
    :index-of-parent-list="indexOfParentList">
    <div :key="widget.id" :class="{ selected: selected }" @click.stop="selectWidget(widget)">
      <el-tabs :type="widget.options.tabType || ''" :tab-position="widget.options.tabPosition"
        :stretch="widget.options.stretch" v-model="activeTab">
        <el-tab-pane v-for="(tab, index) in widget.tabs" :key="index" :name="tab.options.label"
          @click.native.stop="selectWidget(widget)">
          <span slot="label">
            <svg-icon :icon-class="tab.options.icon" v-if="tab.options.icon" />
            {{ tab.options.label }}</span>
          <draggable :list="tab.widgetList" v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
            handle=".drag-handler" @add="(evt) => onContainerDragAdd(evt, tab.widgetList)" @update="onContainerDragUpdate"
            :move="checkContainerMove">
            <transition-group name="fade" tag="div" class="form-widget-list">
              <template v-for="(subWidget, swIdx) in tab.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id"
                    :parent-list="tab.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"></component>
                </template>
                <template v-else>
                  <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id"
                    :parent-list="tab.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"
                    :design-state="true"></component>
                </template>
              </template>
            </transition-group>
          </draggable>
        </el-tab-pane>
      </el-tabs>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'tab-widget',
  componentName: 'ContainerWidget',
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      activeTab: 'tab 1',
      //
    };
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    //
    activeTab(newValue, oldValue) {
      this.widget.tabs.forEach(e => {
        e.options.active = e.options.label === newValue ? true : false
      })

    },
  },
  mounted() {
    this.widget.tabs[0].options.active = true;
    this.activeTab = this.widget.tabs[0].options.label
  },
  methods: {
    // onTabClick(evt) {
    //   let paneName = evt.name;
    //   this.widget.tabs.forEach((tp) => {
    //     tp.options.active = tp.options.name === paneName;
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped>
.selected {
  outline: 2px solid #409eff !important;
}
::v-deep .el-tabs__content{
   min-height: 60px;
  padding: 5px;
  box-sizing: content-box;
}
</style>
