<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      style="min-height: 100px"
      :class="[selected ? 'selected' : '', customClass]"
      class="dialog-body-container"
      @click.stop="selectWidget(widget)"
    >
      <div v-if="widget.widgetList.length === 0" class="no-widget-hint">
        迭代组件内容
      </div>
      <draggable
        :list="widget.widgetList"
        v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
        handle=".drag-handler"
        @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
        @update="onContainerDragUpdate"
        :move="checkContainerMove"
      >
        <transition-group
          name="fade"
          tag="div"
          class="form-widget-list"
          :style="{ height: widget.widgetList.length ? 'auto' : '68px' }"
        >
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-widget'"
                :widget="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
                :design-state="true"
              ></component>
            </template>
          </template>
        </transition-group>
      </draggable>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'iteration-widget',
  componentName: 'ContainerWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      //
    };
  },

  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    //
  },
  mounted() {},
  methods: {
    setIterationCount(data) {
      this.setValue(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-body-container {
  min-height: 50px;
  outline: 1px dashed #336699;
  padding: 6px;
}

.dialog-body-container.selected {
  outline: 2px solid #409eff !important;
}
.no-widget-hint {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  color: #999999;
}
</style>
