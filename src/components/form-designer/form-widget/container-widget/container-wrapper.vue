<template>
  <div class="container-wrapper">
    <slot></slot>
    <div
      class="container-action"
      v-if="designer.selectedId === widget.id && !widget.internal"
    >
      <i
        class="el-icon-back"
        title="选中前一个节点(ctrl + ←)"
        @click.stop="selectBroWidget('last')"
      ></i>
      <i
        class="el-icon-right"
        title="选中后一个节点(ctrl + →)"
        @click.stop="selectBroWidget('next')"
      ></i>
      <i
        class="el-icon-top"
        title="选中父节点(ctrl + ↑)"
        @click.stop="selectParentWidget()"
      ></i>
      <i
        class="el-icon-bottom"
        title="选中子节点(ctrl + ↓)"
        @click.stop="selectSubWidget"
      ></i>
      <i
        v-if="widget.type === 'table'"
        class="iconfont icon-insertrow"
        :title="i18nt('designer.hint.insertRow')"
        @click.stop="insertTableRow(widget)"
      ></i>
      <i
        v-if="widget.type === 'table'"
        class="iconfont icon-insertcolumn"
        :title="i18nt('designer.hint.insertColumn')"
        @click.stop="insertTableCol(widget)"
      ></i>
      <i
        class="el-icon-caret-top"
        title="上移组件"
        @click.stop="moveUpWidget()"
      ></i>
      <i
        class="el-icon-caret-bottom"
        title="下移组件"
        @click.stop="moveDownWidget()"
      ></i>
      <i
        class="el-icon-copy-document"
        v-if="widget.type === 'grid' || widget.type === 'table'"
        :title="i18nt('designer.hint.cloneWidget')"
        @click.stop="cloneContainer(widget)"
      ></i>
      <i
        class="el-icon-delete"
        title="移除组件(delete)"
        @click.stop="removeWidget"
      ></i>
    </div>
    <div
      class="drag-handler"
      v-if="designer.selectedId === widget.id && !widget.internal"
    >
      <i class="el-icon-rank" :title="i18nt('designer.hint.dragHandler')"></i>
      <i>{{ widgetLabel }}</i>
      <i v-if="widget.options.hidden === true" class="el-icon-view"></i>
    </div>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin";
import copyMixin from "@/components/form-designer/copyMixin.js";
import bus from "@/scripts/bus";
export default {
  name: "container-wrapper",
  mixins: [i18n, containerMixin, copyMixin],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  mounted() {
    if (!bus._events[`keydown${this.widget.id}`]) {
      bus.$on(`keydown${this.widget.id}`, (e) => {
        if (e.code == "Delete") {
          this.removeWidget();
        } else if (e.ctrlKey && e.keyCode == 37) {
          this.selectBroWidget("last");
        } else if (e.ctrlKey && e.keyCode == 38) {
          this.selectParentWidget();
        } else if (e.ctrlKey && e.keyCode == 39) {
          this.selectBroWidget("next");
        } else if (e.ctrlKey && e.keyCode == 40) {
          this.selectSubWidget();
        }
      });
    }
  },
  computed: {
    customClass() {
      return !!this.widget.options.customClass
        ? this.widget.options.customClass.join(" ")
        : "";
    },
    widgetLabel() {
      let label = this.i18n2t(
        `designer.widgetLabel.${this.widget.type}`,
        `extension.widgetLabel.${this.widget.type}`
      );
      if (label === "弹出层") label = label + "-" + this.widget.options.label;
      return label;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-wrapper {
  position: relative;
  .container-action {
    position: absolute;
    //bottom: -30px;
    bottom: 0;
    right: -2px;
    height: 28px;
    line-height: 28px;
    background: #409eff;
    z-index: 999;

    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
  }

  .drag-handler {
    position: absolute;
    top: -2px;
    //bottom: -24px;  /* 拖拽手柄位于组件下方，有时无法正常拖动，原因未明？？ */
    left: -2px;
    height: 22px;
    line-height: 22px;
    background: #409eff;
    z-index: 9;

    i {
      font-size: 14px;
      font-style: normal;
      color: #fff;
      margin: 4px;
      cursor: move;
    }
  }
}
</style>
