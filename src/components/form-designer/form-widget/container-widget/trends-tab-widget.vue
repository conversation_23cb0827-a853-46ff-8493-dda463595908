<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <div :key="widget.id" 
         :class="{'selected': selected}" @click.stop="selectWidget(widget)">
      <el-tabs :type="widget.options.tabType">
        <el-tab-pane v-for="(item, index) in this.activeTabs" :name="index" :key="index">
          <span slot="label" @dblclick.stop="editTitleIndex = index">
            <span>{{widget.options.label}}</span>
          </span>
          <draggable :list="widget.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
                     handle=".drag-handler"
                     @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
                     @update="onContainerDragUpdate" :move="checkContainerMove">
            <transition-group name="fade" tag="div" class="form-widget-list">
              <template v-for="(subWidget, swIdx) in widget.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                             :index-of-parent-list="swIdx" :parent-widget="widget"></component>
                </template>
                <template v-else>
                  <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                             :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
                </template>
              </template>
            </transition-group>
          </draggable>
        </el-tab-pane>
      </el-tabs>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n"
import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

export default {
  name: "trends-tab-widget",
  componentName: 'ContainerWidget',
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      tabIndex: '0',
      activeTabs: [{title: this.widget.options.label}],
      editTitleIndex: -1
    }
  },
  created() {
    this.registerSubFormToRefList()
    this.initFieldSchemaData()
    this.initEventHandler()
  },
      computed: {
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },

    },
  watch: {},
  mounted() {
  },
  methods: {

  }
}
</script>

