export default {
  handleOnMounted() {
    if (this.widget.options.onMounted) {
      let mountFunc = new Function(this.widget.options.onMounted);
      mountFunc.call(this);
    }
  },
  methods: {
    insertTableRow(widget) {
      this.designer.insertTableRow(widget);

      this.designer.emitHistoryChange();
    },

    insertTableCol(widget) {
      this.designer.insertTableCol(widget);

      this.designer.emitHistoryChange();
    },

    onContainerDragAdd(evt, subList) {
      const newIndex = evt.newIndex;
      if (!!subList[newIndex]) {
        this.designer.setSelected(subList[newIndex]);
      }

      this.designer.emitHistoryChange();
    },

    onContainerDragUpdate() {
      this.designer.emitHistoryChange();
    },

    checkContainerMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },

    selectWidget(widget) {
      this.designer.setSelected(widget);
    },

    selectParentWidget() {
      if (this.parentWidget) {
        this.designer.setSelected(this.parentWidget);
      } 
    },

    selectSubWidget() {
      if(this.widget.type === 'grid'&& this.widget.cols.length>0 ){
        this.designer.setSelected(this.widget.cols[0])
      }else if(this.widget.type === 'table'){
        this.designer.setSelected(this.widget.rows[0].cols[0])
      }else if(this.widget.widgetList&&this.widget.widgetList.length>0){
        this.designer.setSelected(this.widget.widgetList[0])
      }
    },

    selectBroWidget(type){
      if(type=='last' && this.parentList[this.indexOfParentList-1]){
        this.designer.setSelected(this.parentList[this.indexOfParentList-1])
      }else if(type=='next' && this.parentList[this.indexOfParentList+1]){
        this.designer.setSelected(this.parentList[this.indexOfParentList+1]) 
      } 
    },

    moveUpWidget() {
      this.designer.moveUpWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    moveDownWidget() {
      this.designer.moveDownWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    cloneContainer(widget) {
      if (!!this.parentList) {
        let newCon = this.designer.cloneContainer(widget);
        this.parentList.splice(this.indexOfParentList + 1, 0, newCon);
        this.designer.setSelected(newCon);

        this.designer.emitHistoryChange();
      }
    },

    removeWidget() {
      if (!!this.parentList) {
        let nextSelected = null;
        if (this.parentList.length === 1) {
          if (!!this.parentWidget) {
            nextSelected = this.parentWidget;
          }
        } else if (this.parentList.length === 1 + this.indexOfParentList) {
          nextSelected = this.parentList[this.indexOfParentList - 1];
        } else {
          nextSelected = this.parentList[this.indexOfParentList + 1];
        }

        this.$nextTick(() => {
          this.parentList.splice(this.indexOfParentList, 1);
          //if (!!nextSelected) {
          this.designer.setSelected(nextSelected);
          //}

          this.designer.emitHistoryChange();
        });
      }
    },
  },
};
