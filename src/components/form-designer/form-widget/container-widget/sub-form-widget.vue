<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <div :key="widget.id" 
         :class="{'selected': selected}" @click.stop="selectWidget(widget)">
      <div style="width: 100%;border: 1px dashed" :style="{height: widget.widgetList.length? 'auto':'68px'}">
        <draggable :list="widget.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
                   handle=".drag-handler"
                   @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
                   @update="onContainerDragUpdate" :move="checkContainerMove">
                    <transition-group name="fade" tag="div" class="form-widget-list" style="" :style="{height: widget.widgetList.length? 'auto':'200px'}">
                      <template v-for="(subWidget, swIdx) in widget.widgetList">
                        <template>
                          <component :is="subWidget.type + '-widget'" style="width: 200px;display: inline-block;height: auto" :field="subWidget" :designer="designer" :key="subWidget.options.name" :parent-list="widget.widgetList"
                                     :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
                        </template>
                      </template>
                    </transition-group>
        </draggable>
      </div>
    </div>
  </container-wrapper>
</template>

<script>
  import Draggable from 'vuedraggable'
  import i18n from "@/utils/i18n"
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
  import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "sub-form-widget",
    componentName: 'ContainerWidget',
    mixins: [i18n, containerMixin],
    components: {
      ContainerWrapper,
      Draggable,

      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
    },
    data() {
      return {
        activeTab: 'tab1',
        //
      }
    },
    computed: {
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },

    },
    watch: {
      //
    },
    mounted() {
      //
    },
    methods: {
    }
  }
</script>


