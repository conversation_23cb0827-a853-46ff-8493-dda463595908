<template>
  <div class="form-widget-container">
    <div v-if="groupDialog">
      <el-tabs
        v-model="activeName"
        style="padding: 0px 10px 0 10px"
      >
        <el-tab-pane
          v-for="item in tabs"
          :label="item.name"
          :key="item.id"
          :name="item.id"
        />
      </el-tabs>
      <el-form
        class="full-height-width widget-form"
        :label-position="labelPosition"
        :class="[customClass, layoutType + '-layout']"
        :size="size"
        :validate-on-rule-change="false"
      >
        <div v-if="designer.widgetList.length === 0" class="no-widget-hint">
          {{ i18nt('designer.noWidgetHint') }}
        </div>

        <draggable
          v-if="activeName == 'vfrom'"
          :list="designer.widgetList"
          v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 300 }"
          handle=".drag-handler"
          @end="onDragEnd"
          @add="onDragAdd"
          @update="onDragUpdate"
          :move="checkMove"
        >
          <transition-group name="fade" tag="div" class="form-widget-list">
            <template v-for="(widget, index) in designer.widgetList">
              <template v-if="widget.type != 'dialog'">
                <template v-if="'container' === widget.category">
                  <component
                    :is="getWidgetName(widget)"
                    :widget="widget"
                    :designer="designer"
                    :key="widget.id"
                    :parent-list="designer.widgetList"
                    :index-of-parent-list="index"
                    :parent-widget="null"
                  ></component>
                </template>
                <template v-else>
                  <component
                    :is="getWidgetName(widget)"
                    :isFieldList="true"
                    :field="widget"
                    :designer="designer"
                    :key="widget.id"
                    :parent-list="designer.widgetList"
                    :index-of-parent-list="index"
                    :parent-widget="null"
                    :design-state="true"
                  ></component>
                </template>
              </template>
            </template>
          </transition-group>
        </draggable>
        <template v-if="activeName != 'vfrom'">
          <template v-for="(widget, index) in designer.widgetList">
            <template v-if="widget.type == 'dialog' && activeName == widget.id">
              <template v-if="'container' === widget.category">
                <component
                  :is="getWidgetName(widget)"
                  :widget="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                ></component>
              </template>
              <template v-else>
                <component
                  :is="getWidgetName(widget)"
                  :isFieldList="true"
                  :field="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :design-state="true"
                ></component>
              </template>
            </template>
          </template>
        </template>
      </el-form>
    </div>
    <div v-else>
      <el-form
        class="full-height-width widget-form"
        :label-position="labelPosition"
        :class="[customClass, layoutType + '-layout']"
        :size="size"
        :validate-on-rule-change="false"
      >
        <div v-if="designer.widgetList.length === 0" class="no-widget-hint">
          {{ i18nt('designer.noWidgetHint') }}
        </div>

        <draggable
          :list="designer.widgetList"
          v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 300 }"
          handle=".drag-handler"
          @end="onDragEnd"
          @add="onDragAdd"
          @update="onDragUpdate"
          :move="checkMove"
        >
          <transition-group name="fade" tag="div" class="form-widget-list">
            <template v-for="(widget, index) in designer.widgetList">
              <template v-if="'container' === widget.category">
                <component
                  :is="getWidgetName(widget)"
                  :widget="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                ></component>
              </template>
              <template v-else>
                <component
                  :is="getWidgetName(widget)"
                  :isFieldList="true"
                  :field="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :design-state="true"
                ></component>
              </template>
            </template>
          </transition-group>
        </draggable>
      </el-form>
    </div>
  </div>
</template>

<script>
import Draggable from 'vuedraggable';
import '@/components/form-designer/form-widget/container-widget/index';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import i18n from '@/utils/i18n';
import bus from '@/scripts/bus';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
export default {
  name: 'VFormWidget',
  componentName: 'VFormWidget',
  mixins: [i18n, containerMixin],
  components: {
    Draggable,

    ...FieldComponents,
  },
  props: {
    designer: Object,
    formConfig: Object,
    optionData: {
      //prop传入的选项数据
      type: Object,
      default: () => {},
    },
  },
  provide() {
    return {
      refList: this.widgetRefList,
      formConfig: this.formConfig,
      globalOptionData: this.optionData,
      openDialogTableShow: null,
      globalModel: {
        formModel: this.formModel,
      },
    };
  },
  watch: {
    'designer.widgetList': {
      immediate: true,
      handler(val) {
        let tabs = [{ name: '主界面', id: 'vfrom' }];

        let cr = (arr) => {
          arr.forEach((item) => {
            try {
              if (item.type == 'dialog') {
                tabs.push({
                  name: item.options.mark
                    ? item.options.mark
                    : item.options.label,
                  id: item.id,
                });
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols);
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList);
              }
            } catch (e) {
              // 里面可能有栅格等字段没有清除接口
            }
          });
        };
        //迭代
        cr(this.designer.widgetList);
        this.tabs = tabs;
        let nowTabId = 'vfrom';
        for (let i = 0; i < tabs.length; i++) {
          if (this.activeName == tabs[i].id) {
            nowTabId = tabs[i].id;
          }
        }
        this.activeName = nowTabId;
      },
      deep: true,
    },
    tabs: {
      handler(val, old) {
        if (this.flag) {
          if (val.length > old.length) {
            for (let i = 0; i < val.length; i++) {
              if (!old[i] || val[i].id !== old[i].id) {
                this.activeName = val[i].id;
                break;
              }
            }
          } else if (val.length < old.length) {
            this.activeName = 'vfrom';
          }
        }
      },
      immediate: false,
    },
    activeName: {
      handler(val, old) {
        this.$array.handleWidgetList(this.designer.widgetList, (item) => {
          if (item.id === val) {
            this.selectWidget(item);
          }
        });
      },
    },
  },
  data() {
    return {
      formModel: {},
      widgetRefList: {},
      activeName: 'vfrom',
      tabs: [],
      groupDialog: true,
      flag:false
    };
  },
  computed: {
    labelPosition() {
      if (
        !!this.designer.formConfig &&
        !!this.designer.formConfig.labelPosition
      ) {
        return this.designer.formConfig.labelPosition;
      }
      return 'left';
    },
    size() {
      if (!!this.designer.formConfig && !!this.designer.formConfig.size) {
        return this.designer.formConfig.size;
      }
      return 'medium';
    },
    customClass() {
      return this.designer.formConfig.customClass || '';
    },
    layoutType() {
      return this.designer.getLayoutType();
    },
  },
  created() {
    this.designer.initDesigner();

    // 监听任务
    bus.$on('onVFromGroupDialogChange', (flag) => {
      // 编辑界面是否将弹窗分组
      this.groupDialog = flag;
    });
  },

  mounted() {
    this.disableFirefoxDefaultDrop(); /* 禁用Firefox默认拖拽搜索功能!! */
    this.designer.registerFormWidget(this);
    window.addEventListener('keydown', this.handleEvent);
    setTimeout(()=>{
      this.flag=true
    },1000)
  },
  methods: {
    handleEvent(e) {
      bus.$emit(`keydown${this.designer.selectedId}`, e);
      bus.$emit('shortcutKeys', e);
      // 阻止默认事件
      // e.preventDefault();
    },
    getWidgetName(widget) {
      return widget.type + '-widget';
    },

    disableFirefoxDefaultDrop() {
      let isFirefox =
        navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;
      if (isFirefox) {
        document.body.ondrop = function (event) {
          event.stopPropagation();
          event.preventDefault();
        };
      }
    },

    onDragEnd(evt) {
      //console.log('drag end000', evt)
    },

    onDragAdd(evt) {
      const newIndex = evt.newIndex;
      if (!!this.designer.widgetList[newIndex]) {
        this.designer.setSelected(this.designer.widgetList[newIndex]);
      }

      this.designer.emitHistoryChange();
    },

    onDragUpdate() {
      /* 在VueDraggable内拖拽组件发生位置变化时会触发update，未发生组件位置变化不会触发！！ */
      this.designer.emitHistoryChange();
    },

    checkMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },

    getFormData() {
      return this.formModel;
    },

    getWidgetRef(widgetName, showError) {
      let foundRef = this.widgetRefList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(
          this.i18nt('designer.hint.refNotFound') + widgetName,
        );
      }
      return foundRef;
    },
  },
  beforeDestroy() {
    // 页面销毁 解除
    window.removeEventListener('keydown', this.handleEvent);
  },
};
</script>

<style lang="scss" scoped>
.container-scroll-bar {
  ::v-deep .el-scrollbar__wrap,
  ::v-deep .el-scrollbar__view {
    overflow-x: hidden;
  }
}

.form-widget-container {
  padding: 5px 10px;
  background: transparent;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;

  .el-form.full-height-width {
    /*
      margin: 0 auto;
      width: 420px;
      border-radius: 15px;
      //border-width: 10px;
      box-shadow: 0 0 1px 10px #495060;
      */

    height: 100%;
    padding: 3px;
    background: transparent;

    .no-widget-hint {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 18px;
      color: #999999;
    }

    .form-widget-list {
      min-height: calc(100vh - 255px);
      padding: 3px;
    }
  }

  .el-form.PC-layout {
    //
  }

  .el-form.Pad-layout {
    margin: 0 auto;
    width: 960px;
    border-radius: 15px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.H5-layout {
    margin: 0 auto;
    width: 420px;
    border-radius: 15px;
    //border-width: 10px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.widget-form ::v-deep .el-row {
    padding: 2px;
    border: 1px dashed rgba(170, 170, 170, 0.75);
  }
}

.grid-cell {
  min-height: 30px;
  border-right: 1px dotted #cccccc;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
