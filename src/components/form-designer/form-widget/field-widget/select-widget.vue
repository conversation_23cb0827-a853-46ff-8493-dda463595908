<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <el-select v-if="field" ref="fieldEditor" v-model="fieldModel" :style="{ color: field.options.color }"
      :class="['hide-spin-button', field.options.color ? 'color' : '']" :disabled="field.options.disabled"
      :size="field.options.size" :clearable="field.options.clearable" :filterable="field.options.filterable"
      :allow-create="field.options.allowCreate" :default-first-option="allowDefaultFirstOption"
      :filter-method="handleFilter" :automatic-dropdown="field.options.automaticDropdown"
      :multiple="field.options.multiple" :multiple-limit="field.options.multipleLimit" :placeholder="field.options.placeholder ||
        i18nt('render.hint.selectPlaceholder') + field.options.label
        " :collapse-tags="field.options.collapseTag" :remote="this.field.options.remote" :remote-method="remoteQuery"
      @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent" @change="handleChangeEvent"
      @visible-change="visibleChange">
      <el-option v-for="item in selectData" :key="item[getValueField]" :label="item[getLabelField]"
        :value="item[getValueField]" :disabled="item.disabled">
        <span style="float: left">{{ item[getLabelField] }}</span>
        <span v-if="item._describe != null" style="float: right; color: #8492a6; font-size: 13px">{{ item._describe
        }}</span>
      </el-option>
    </el-select>
  </form-item-wrapper>
</template>

<script>
import PinyinMatch from 'pinyin-match';
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { executeInterface } from '@/api/interfaces/interfaces';
export default {
  name: 'select-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      selectItem: null,
      selectData: [],
      copySelectData: [],
      rules: [],
      dataType: 'string',
    };
  },
  watch: {
    // globalModel: {
    //   immediate: true,
    //   handler(val) {
    //     this.initFieldModel();
    //     this.splitValues();
    //   },
    //   deep: true,
    // },
    'field.options.optionItems': {
      immediate: true,
      handler(val) {
        this.selectData = this.field.options.optionItems;
        this.copySelectData = this.field.options.optionItems;
        // 转换选项中的value值为string
        this.itemToStr();
      },
      deep: true,
    },
    fieldModel(newVal, oldVal) {
      if (!newVal || !newVal.length) return;
      if (typeof newVal === 'object') {
        let disposeArr = newVal.filter(item => !oldVal.includes(item)).concat(oldVal.filter(item => !newVal.includes(item)));
        if (newVal.length > oldVal.length && disposeArr.length) {
          this.existIncludeValue(disposeArr[0])
        }
      } else {
        this.existIncludeValue(newVal)
      }
    }
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable && !!this.field.options.allowCreate
      );
    },
    color() {
      if (!!this.field.options.color) {
        return this.field.options.color;
      }
    },
    getLabelField() {
      return this.field.options.selectLabel || 'label';
    },
    getValueField() {
      return this.field.options.selectValue || 'value';
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
    // this.splitValues();
  },

  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    existIncludeValue(value) {  //是否存在创建项value
      let isTrue = false;
      this.selectData.forEach(e => {
        if (e.value === value) {
          isTrue = true;
        }
      })
      if (!isTrue && this.field.options.allowCreateFn) {
        let JS = new Function("value", this.field.options.allowCreateFn)
        JS.call(this, value)
      }
    },
    sendApi() {
      if (this.field.options.api) {
        // 获取参数配置
        let param = this.getRequestParam();

        executeInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.selectData = res.data;
            if (this.selectData.length) {
              this.dataType = typeof this.selectData[0][this.getValueField];
            }

            this.setSelectItem(
              this.selectData.filter(
                (item) => item[this.getValueField] === this.fieldModel,
              )[0],
            );

            this.copySelectData = Object.assign(this.selectData);
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      } else if (this.field.options.optionItems) {
        this.initOptionsItem(this.field.options.optionItems);
      }
    },
    // 将item选项中的value的类型转换成String类型
    itemToStr() {
      if (this.selectData && this.selectData.length > 0) {
        this.selectData.forEach((item) => {
          if (
            item[this.getValueField] &&
            typeof item[this.getValueField] == 'number'
          ) {
            item[this.getValueField] = item[this.getValueField].toString();
          }
        });
      }
      if (this.copySelectData && this.copySelectData.length > 0) {
        this.copySelectData.forEach((item) => {
          if (
            item[this.getValueField] &&
            typeof item[this.getValueField] == 'number'
          ) {
            item[this.getValueField] = item[this.getValueField].toString();
          }
        });
      }
    },
    handleFilter(val) {
      // this.fieldModel=val
      // 对绑定数据赋值
      if (val) {
        this.selectData = this.copySelectData.filter((item) => {
          // 如果直接包含输入值直接返回true
          let label = item[this.getLabelField];
          if (label) {
            if (label.toUpperCase().includes(val.toUpperCase())) {
              return true;
            }
            // 输入值拼音d
            return PinyinMatch.match(label, val);
          }
        });
      } else {
        this.selectData = this.copySelectData;
      }
    },
    splitValues() {
      if (this.field.options.multiple && this.fieldModel) {
        if (typeof this.fieldModel == 'string') {
          this.fieldModel = this.fieldModel.split(',');
          // this.setSelectItem(
          //   this.selectData.filter((item) => this.fieldModel.includes(item[this.getValueField]))[0],
          // );
        }
      }
    },
    setSelectItem(item) {
      this.selectItem = item;
      // 转换选项中的value值为string
      this.itemToStr();
    },
    initOptionsItem(item) {
      this.selectData = item;
      this.copySelectData = Object.assign(item);
    },
    getSelectItem() {
      // if (this.selectItem) {
      //   return this.selectItem;
      // }
      if (
        (this.field.options.optionItems != null &&
          this.field.options.optionItems.length > 0) ||
        (this.selectData != null && this.selectData.length > 0)
      ) {
        let items = this.selectData || this.field.options.optionItems;
        if (this.field.options.multiple) {
          return items.filter(
            (item) => this.fieldModel.includes(item[this.getValueField]),
          )
        } else {
          return items.filter(
            (item) => item[this.getValueField] == this.fieldModel,
          )[0];
        }

      } else {
        return null;
      }
    },
    visibleChange(e) {
      if (!e) {
        setTimeout(() => {
          this.selectData = JSON.parse(JSON.stringify(this.copySelectData));
        }, 100);
      }
    },
    getSelectData() {
      return this.selectData;
    },
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}


.full-width-input {
  width: 100% !important;
}
</style>
<style scoped>
::v-deep .el-select {
  width: 100%;
}
</style>
