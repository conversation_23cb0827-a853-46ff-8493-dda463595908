

<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div v-if="field.options.type == 'yearrange'" style="display: flex">
      <el-date-picker
        type="year"
        v-model="begin"
        class="full-width-input"
        :disabled="field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :editable="field.options.editable"
        format="yyyy年"
        :value-format="field.options.valueFormat"
      >
      </el-date-picker>
      <span style="margin: 0 5px">~</span>
      <el-date-picker
        type="year"
        v-model="end"
        class="full-width-input"
        :disabled="field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :editable="field.options.editable"
        format="yyyy年"
        :value-format="field.options.valueFormat"
      >
      </el-date-picker>
    </div>
    <el-date-picker
      v-else
      ref="fieldEditor"
      :type="field.options.type"
      v-model="processValue"
      class="full-width-input"
      :disabled="field.options.disabled"
      :readonly="field.options.readonly"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :editable="field.options.editable"
      :format="field.options.format"
      :value-format="field.options.valueFormat"
      :picker-options="pickerOptions"
      :start-placeholder="
        field.options.startPlaceholder ||
        i18nt('render.hint.startDatePlaceholder')
      "
      :end-placeholder="
        field.options.endPlaceholder || i18nt('render.hint.endDatePlaceholder')
      "
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @change="handleChangeEvent"
    >
    </el-date-picker>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from "./form-item-wrapper";
import emitter from "element-ui/lib/mixins/emitter";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
let timer = null;

export default {
  name: "date-range-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      processValue: null,
      begin: null,
      end: null,
    };
  },
  computed: {
    pickerOptions() {
      if (!this.field.options.shortcuts) {
        return false;
      } else {
        if (this.field.options.type == "monthrange") {
          return {
            shortcuts: [
              {
                text: "本月",
                onClick(picker) {
                  picker.$emit("pick", [new Date(), new Date()]);
                },
              },
              {
                text: "最近三个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setMonth(start.getMonth() - 3);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "今年至今",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(new Date().getFullYear(), 0);
                  picker.$emit("pick", [start, end]);
                },
              },
            ],
          };
        } else if (
          this.field.options.type == "daterange" ||
          this.field.options.type == "datetimerange"
        ) {
          return {
            shortcuts: [
              {
                text: "今天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(new Date().toLocaleDateString());
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近一周",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近一个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近三个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit("pick", [start, end]);
                },
              },
            ],
          };
        }
      }
    },
  },

  watch: {
    begin(n, o) {
      this.setValue(n + "," + this.end);
    },
    end(n, o) {
      this.setValue(this.begin + "," + n);
    },
    "field.options": {
      handler(newVal, oldVal) {
        this.setCurrentDate();
      },
      deep: true,
    },
    fieldModel: {
      handler(val) {
        if (val != "" && val != null) {
          if (typeof val == "string") {
            this.processValue = val.split(",");
          }
          this.$forceUpdate();
        }
      },
      deep: true,
      immediate: true,
    },
    processValue: {
      handler(val) {
        if (val && val != "") {
          this.fieldModel = val.join();
          this.$forceUpdate();
        } else {
          this.fieldModel = "";
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
    this.setCurrentDate();
    if (!this.field.options.hasOwnProperty("shortcuts")) {
      this.$set(this.field.options, "shortcuts", true);
    }
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    setCurrentDate() {
      function getDateStr(now) {
        var year = now.getFullYear(); // 年
        var month = now.getMonth() + 1; // 月
        var day = now.getDate(); // 日
        if (day < 10) {
          day = "0" + day;
        }

        if (month < 10) {
          month = "0" + month;
        }
        return year + "-" + month + "-" + day;
      }

      const { defaultRange, type, valueFormat } = this.field.options;
      if (defaultRange == "day") {
        let currentDate = this.$date.formatDate(new Date(), "yyyy-MM-dd");
        let startDate = currentDate;
        let endDate = currentDate;

        if (valueFormat === "yyyy-MM-dd HH:mm:ss") {
          startDate += " 00:00:00";
          endDate += " 23:59:59";
        }
        this.setValue(startDate + "," + endDate);
      } else if (defaultRange == "week") {
        var now = new Date();
        var nowDayOfWeek = now.getDay() == 0 ? 6 : now.getDay() - 1;
        var startDate = getDateStr(
          new Date(now.getTime() - nowDayOfWeek * 24 * 60 * 60 * 1000)
        );
        var endDate = getDateStr(
          new Date(now.getTime() + (6 - nowDayOfWeek) * 24 * 60 * 60 * 1000)
        );

        if (valueFormat === "yyyy-MM-dd HH:mm:ss") {
          startDate += " 00:00:00";
          endDate += " 23:59:59";
        }
        this.setValue(startDate + "," + endDate);
      } else if (defaultRange == "month") {
        var now = new Date();
        var nowDay = now.getDate() - 1;
        var startDate = new Date();
        startDate.setDate(1);
        startDate = getDateStr(startDate);
        var endDate = new Date(
          new Date().getFullYear(),
          new Date().getMonth() + 1,
          0
        );
        endDate = getDateStr(endDate);

        if (valueFormat === "yyyy-MM-dd HH:mm:ss") {
          startDate += " 00:00:00";
          endDate += " 23:59:59";
        }
        this.setValue(startDate + "," + endDate);
      } else {
        this.setValue("");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.full-width-input {
  width: 100% !important;
}
</style>
