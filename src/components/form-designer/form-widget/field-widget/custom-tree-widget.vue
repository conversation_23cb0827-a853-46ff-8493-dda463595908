<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <el-input
      v-if="field.options.hasSearch"
      :placeholder="field.options.placeholder || '输入关键字进行过滤'"
      prefix-icon="el-icon-search"
      size="mini"
      v-model="filterText"
    />
    <div
      style="padding-top: 10px; overflow: auto"
      :style="{ height: computedHeight }"
      @scroll="scrollChange"
    >
      <el-tree
        :id="`custom-true-${stringToIntHash(field.id)}`"
        ref="tree"
        :loading="loading"
        :node-key="field.options.nodeKey || 'id'"
        :data="fieldModel"
        :props="defaultProps"
        :show-checkbox="field.options.showCheckbox"
        :empty-text="field.options.emptyText"
        :indent="field.options.indent"
        :icon-class="field.options.iconClass"
        :default-expanded-keys="defaultExpandedKeys"
        :default-checked-keys="defaultCheckedKeys"
        :default-expand-all="field.options.expandAll"
        :expand-on-click-node="field.options.expandOnClickNode"
        :check-strictly="!field.options.checkStrictly"
        @check="handleCheckChange"
        @node-click="handleNodeClick"
        :filter-node-method="filterNode"
        :lazy="field.options.isLazy"
        :load="loadNode"
      >
        <template slot-scope="{ node, data }">
          <div
            class="custom-tree-node"
            style="
              display: flex;
              flex-direction: row;
              width: 100%;
              justify-content: space-between;
            "
          >
            <div
              v-html="renderContent(node, data)"
              style="
                width: 69%;
                scroll-behavior: auto;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            ></div>
            <div v-if="field.options.showRightText">
              <el-button
                v-if="
                  data.children &&
                  data.children.length !== 0 &&
                  node.expanded == true
                "
                type="text"
                class="node-tree-node__expand-icon"
                size="mini"
                @click.stop="node.expanded = false"
                >缩收</el-button
              >
              <el-button
                v-if="
                  data.children &&
                  data.children.length !== 0 &&
                  node.expanded == false
                "
                type="text"
                class="node-tree-node__expand-icon"
                size="mini"
                @click.stop="node.expanded = true"
                >展开</el-button
              >
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </view-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import i18n from '@/utils/i18n';
import FormItemWrapper from '@/components/form-designer/form-widget/field-widget/form-item-wrapper';
import ViewWrapper from '@/components/form-designer/form-widget/field-widget/view-wrapper';
import { executeInterface } from '@/api/interfaces/interfaces';
import bus from '@/magic-editor/scripts/bus';

export default {
  name: 'custom-tree-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ViewWrapper,
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      fieldModel: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      filterText: '',
      loading: false,
      clickNode: null,
      defaultExpandedKeys: [],
      defaultCheckedKeys: [],
      computedHeight: '',
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  computed: {},
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initFieldModel();
    // 获取树数据
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },

  mounted() {
    this.handleOnMounted();
    if (this.field.options.heightFit) {
      bus.$on('resizeHeight', this.resizeHeight);
      if (this.field.options.heightFit) {
        this.resizeHeight();
      }
    }
    if(!this.field.options.hasOwnProperty('expandOnClickNode')){
      this.$set(this.field.options, 'expandOnClickNode', true);
    }
    if(!this.field.options.hasOwnProperty('showRightText')){
      this.$set(this.field.options, 'showRightText', true);
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    resizeHeight() {
      let hasHidden = $(
        '#custom-true-' + this.stringToIntHash(this.field.id),
      ).is(':visible');
      if (!hasHidden) {
        setTimeout(() => {
          this.resizeHeight();
        }, 200);
      } else {
        let h =
          $('#custom-true-' + this.stringToIntHash(this.field.id)).offset()
            .top + 30;
        this.computedHeight = `calc(100vh - ${h}px)`;
      }
    },
    stringToIntHash(str, upperbound, lowerbound) {
      let result = 0;
      for (let i = 0; i < str.length; i++) {
        result = result + str.charCodeAt(i);
      }

      if (!lowerbound) lowerbound = 0;
      if (!upperbound) upperbound = 500;

      return (result % (upperbound - lowerbound)) + lowerbound;
    },
    loadNode(node, resolve) {
      if (this.field.options.onLazy) {
        let js = new Function('node', 'resolve', this.field.options.onLazy);
        return js.call(this, node, resolve);
      } else {
        if (node.level > 0) {
          if (node.data.children) {
            resolve(node.data.children);
          } else {
            resolve([]);
          }
        }
      }
    },
    /**
     * 通过key设置节点
     * @param {*} arr
     */
    setCheckedKeys(arr) {
      this.$refs.tree.setCheckedKeys(arr);
    },
    /**
     * 通过key获取节点
     */
    getCheckedKeys() {
      return this.$refs.tree.getCheckedKeys();
    },
    // 设置默认选中节点
    setDefaultCheckedKeys(arr) {
      this.defaultCheckedKeys = arr;
      let checkNode = [];
      function iter(ls) {
        for (let i in ls) {
          if (arr.includes(ls[i].id)) {
            checkNode.push(ls[i]);
          }
          if (ls[i].children && ls[i].children != []) {
            iter(ls[i].children);
          }
        }
      }
      iter(this.fieldModel);
      this.checkNode = checkNode;
    },
    // 设置默认展开节点
    setDefaultExpandedKeys(arr) {
      this.defaultExpandedKeys = arr;
    },
    setDefaultClickNode(node) {
      this.clickNode = node;
    },
    sendApi() {
      if (this.field.options.api) {
        this.loading = true;
        executeInterface({
          apiId: this.field.options.api,
        })
          .then((res) => {
            this.fieldModel = res.data;
            this.loading = false;
            if (this.field.options.onSuccessCallback) {
              let success = new Function(
                'res',
                this.field.options.onSuccessCallback,
              );
              success.call(this, res);
            }
          })
          .catch((err) => {
            this.loading = false;
            // this.$message.error(err.message);
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    renderContent(node, data) {
      if (this.field.options.onRenderContent) {
        let changeFn = new Function(
          'node',
          'data',
          this.field.options.onRenderContent,
        );
        return changeFn.call(this, node, data);
      } else {
        let styleStr = '';
        if (this.field.options.onNodeStyle) {
          let fun = new Function(
            'node',
            'data',
            this.field.options.onNodeStyle,
          );
          let temp = fun.call(this, node, data);
          for (let key in temp) {
            styleStr += `${key}: ${temp[key]};`;
          }
        }

        return `<span style="${styleStr}">${node.label}</span>`;
      }
    },
    // 点击node触发
    handleNodeClick(e) {
      this.clickNode = e;
      if (this.field.options.onNodeClick) {
        let changeFn = new Function('node', this.field.options.onNodeClick);
        changeFn.call(this, e);
      }
    },
    // 选择节点触发
    handleCheckChange(checkedNodes, checkedKeys) {
      this.checkNode = checkedKeys.checkedNodes;
      if (this.field.options.onNodeCheck) {
        let changeFn = new Function(
          'node',
          'nodes',
          this.field.options.onNodeCheck,
        );
        changeFn.call(this, checkedNodes, checkedKeys.checkedNodes);
      }
    },
    // 过滤方法
    filterNode(value, data) {
      if (this.field.options.onFilterNodeMethod) {
        let customFunc = new Function(
          'value',
          'data',
          this.field.options.onFilterNodeMethod,
        );
        return customFunc.call(this, value, data);
      }
      return data.label.includes(value);
    },
    getClickNode() {
      return this.clickNode;
    },
    getCheckNodes() {
      return this.checkNode;
    },
    getDataSource() {
      return this.fieldModel;
    },
    setDataSource(data) {
      this.fieldModel = data;
    },
    scrollChange(e) {
      if (
        e.target.scrollTop + e.target.clientHeight >=
        e.target.scrollHeight - 1
      ) {
        if (this.field.options.onScrollEnd) {
          let JS = new Function(this.field.options.onScrollEnd);
          JS.call(this);
        }
      }
    },
    setLoading(val) {
      this.loading = val;
    },
  },
};
</script>

<style lang="scss" scoped>


.full-width-input {
  width: 100% !important;
}
</style>
