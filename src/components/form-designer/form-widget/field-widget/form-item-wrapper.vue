<template>
  <div
    class="field-wrapper"
    :class="{ 'design-time-bottom-margin': !!this.designer }"
  >
    <el-form-item
      v-if="!!field.formItemFlag"
      :label="label"
      :label-width="labelWidth + 'px'"
      v-show="!field.options.hidden || designState === true"
      :rules="rules"
      :prop="getPropName()"
      :style="{ color: field.options.labelColor }"
      :class="[
        selected ? 'selected' : '',
        labelAlign,
        field.options.labelColor ? 'labelColor' : '',
        customClass,
        field.options.required ? 'required' : '',
      ]"
      @click.native.stop="selectField(field)"
    >
      <template
        v-if="
          !!field.options.labelTooltip && field.options.labelTooltip.length > 15
        "
        slot="label"
      >
        <el-popover placement="right" width="400" trigger="hover">
          <div v-html="field.options.labelTooltip"></div>
          <template slot="reference">
            <span
              v-if="!!field.options.labelIconClass"
              slot="label"
              class="custom-label"
            >
              <template v-if="field.options.labelIconPosition === 'front'">
                <template
                  ><i :class="field.options.labelIconClass"></i
                  >{{ label }}</template
                >
              </template>
              <template v-else-if="field.options.labelIconPosition === 'rear'">
                <template>
                  {{ label }}<i :class="field.options.labelIconClass"></i
                ></template>
              </template>
            </span>
            <span class="custom-label">{{ label }}</span>
          </template>
        </el-popover>
      </template>
      <span
        v-if="!!field.options.labelIconClass"
        slot="label"
        class="custom-label"
      >
        <template v-if="field.options.labelIconPosition === 'front'">
          <template v-if="!!field.options.labelTooltip">
            <el-tooltip :content="field.options.labelTooltip" effect="light">
              <i :class="field.options.labelIconClass"></i></el-tooltip
            >{{ label }}</template
          >
          <template v-else
            ><i :class="field.options.labelIconClass"></i>{{ label }}</template
          >
        </template>
        <template v-else-if="field.options.labelIconPosition === 'rear'">
          <template v-if="!!field.options.labelTooltip">
            {{ label
            }}<el-tooltip :content="field.options.labelTooltip" effect="light">
              <i :class="field.options.labelIconClass"></i></el-tooltip
          ></template>
          <template v-else>
            {{ label }}<i :class="field.options.labelIconClass"></i
          ></template>
        </template>
      </span>
      <div v-if="subEditFlag != null">
        <span v-if="!subEditFlag[subFormRowId]">
          {{ value }}
        </span>
        <template v-else>
          <slot></slot>
        </template>
      </div>
      <div v-else>
        <slot></slot>
      </div>
    </el-form-item>

    <template v-if="!!this.designer">
      <div class="field-action" v-if="designer.selectedId === field.id">
        <i
          class="el-icon-back"
          title="选中前一个节点(ctrl + ←)"
          @click.stop="selectBroWidget('last')"
        ></i>
        <i
          class="el-icon-right"
          title="选中后一个节点(ctrl + →)"
          @click.stop="selectBroWidget('next')"
        ></i>
        <i
          class="el-icon-top"
          title="选中父节点(ctrl + ↑)"
          @click.stop="selectParentWidget"
        ></i>
        <i
          class="el-icon-copy-document"
          :title="i18nt('designer.hint.cloneWidget')"
          @click.stop="cloneField(field)"
        ></i>
        <i
          class="el-icon-delete"
          title="移除组件(delete)"
          @click.stop="removeFieldWidget"
        ></i>
      </div>
      <div
        class="drag-handler background-opacity"
        v-if="designer.selectedId === field.id"
      >
        <i class="el-icon-rank" :title="i18nt('designer.hint.dragHandler')"></i>
        <i>{{
          i18n2t(
            `designer.widgetLabel.${field.type}`,
            `extension.widgetLabel.${field.type}`
          )
        }}</i>
        <i v-if="field.options.hidden === true" class="el-icon-view"></i>
      </div>
    </template>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import copyMixin from "@/components/form-designer/copyMixin.js";
import bus from '@/scripts/bus';
export default {
  name: "form-item-wrapper",
  mixins: [i18n, copyMixin],
  props: {
    field: Object,
    designer: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    rules: Array,
  },
  inject: ["formConfig"],
  computed: {
    selected() {
      return !!this.designer && this.field.id === this.designer.selectedId;
    },
    value() {
      let val =
        this.echoFieldData[this.subFormRowIndex][this.field.options.name];
      if (val === "false" || val === false) {
        return "否";
      } else if (val === "true" || val === true) {
        return "是";
      } else {
        return this.echoFieldData[this.subFormRowIndex][
          this.field.options.name
        ];
      }
    },
    label() {
      if (!!this.field.options.labelHidden) {
        return "";
      }

      return this.field.options.label;
    },

    labelWidth() {
      if (!!this.field.options.labelHidden) {
        return 0;
      }

      if (!!this.field.options.labelWidth) {
        return this.field.options.labelWidth;
      }

      if (!!this.designer) {
        return this.designer.formConfig.labelWidth;
      } else {
        return this.formConfig.labelWidth;
      }
    },
    labelColor() {
      if (!!this.field.options.labelColor) {
        return this.field.options.labelColor;
      }
    },
    labelAlign() {
      if (!!this.field.options.labelAlign) {
        return this.field.options.labelAlign;
      }

      if (!!this.designer) {
        return this.designer.formConfig.labelAlign || "label-left-align";
      } else {
        return this.formConfig.labelAlign || "label-left-align";
      }
    },

    customClass() {
      return !!this.field.options.customClass
        ? this.field.options.customClass.join(" ")
        : "";
    },

    subFormName() {
      return !!this.parentWidget ? this.parentWidget.options.name : "";
    },

    subFormItemFlag() {
      return !!this.parentWidget
        ? this.parentWidget.type === "sub-form"
        : false;
    },
    trendsTabFlag() {
      return !!this.parentWidget
        ? this.parentWidget.type === "trends-tab"
        : false;
    },
  },
  mounted() {
    if (!bus._events[`keydown${this.field.id}`]) {
      bus.$on(`keydown${this.field.id}`, (e) => {
        if (e.code == "Delete") {
          this.removeFieldWidget();
        } else if (e.ctrlKey && e.keyCode == 37) {
          this.selectBroWidget("last");
        } else if (e.ctrlKey && e.keyCode == 38) {
          this.selectParentWidget();
        } else if (e.ctrlKey && e.keyCode == 39) {
          this.selectBroWidget("next");
        }
      });
    }
  },
  methods: {
    cloneField(field) {
      let newField = this.designer.copyNewFieldWidget(field);
      this.parentList.splice(this.indexOfParentList + 1, 0, newField);
      this.designer.setSelected(newField);
    },
    selectField(field) {
      if (!!this.designer) {
        this.designer.setSelected(field);
        this.designer.emitEvent("field-selected", this.parentWidget); //发送选中组件的父组件对象
      }
    },

    selectParentWidget() {
      if (this.parentWidget) {
        this.designer.setSelected(this.parentWidget);
      } else {
        this.designer.clearSelected();
      }
    },

    moveUpWidget() {
      this.designer.moveUpWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    moveDownWidget() {
      this.designer.moveDownWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    removeFieldWidget() {
      if (!!this.parentList) {
        let nextSelected = null;
        if (this.parentList.length === 1) {
          if (!!this.parentWidget) {
            nextSelected = this.parentWidget;
          }
        } else if (this.parentList.length === 1 + this.indexOfParentList) {
          nextSelected = this.parentList[this.indexOfParentList - 1];
        } else {
          nextSelected = this.parentList[this.indexOfParentList + 1];
        }

        this.$nextTick(() => {
          this.parentList.splice(this.indexOfParentList, 1);
          //if (!!nextSelected) {
          this.designer.setSelected(nextSelected);
          //}

          this.designer.emitHistoryChange();
        });
      }
    },

    getPropName() {
      if (this.trendsTabFlag && !this.designState) {
        // 返回trendsTab 数据结构
        return (
          this.subFormName +
          "." +
          this.subFormRowIndex +
          "." +
          this.field.options.name +
          ""
        );
      } else if (this.subFormItemFlag && !this.designState) {
        return (
          this.subFormName +
          "." +
          this.subFormRowIndex +
          "." +
          this.field.options.name +
          ""
        );
      } else {
        return this.field.options.name;
      }
    },
  },
};
</script>
<style lang="scss">
.labelColor {
  .el-form-item__label {
    color: var(--labelColor);
  }
}
</style>
<style lang="scss" scoped>
.field-wrapper {
  position: relative;

  .field-action {
    position: absolute;
    //bottom: -24px;
    bottom: 0;
    right: -2px;
    height: 22px;
    line-height: 22px;
    background: #409eff;
    z-index: 9;

    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
  }

  .drag-handler {
    position: absolute;
    top: 0;
    //bottom: -22px;  /* 拖拽手柄位于组件下方，有时无法正常拖动，原因未明？？ */
    left: -1px;
    height: 20px;
    line-height: 20px;
    background: #409eff;
    z-index: 9;

    i {
      font-size: 12px;
      font-style: normal;
      color: #fff;
      margin: 4px;
      cursor: move;
    }

    &:hover {
      //opacity: 1;
      background: #409eff;
    }
  }
}

.el-form-item {
  //margin-bottom: 0 !important;
  //margin-bottom: 6px;

  //margin-top: 2px;
  position: relative;

  ::v-deep .el-form-item__label {
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  ::v-deep .el-form-item__content {
    //position: unset;  /* TODO: 忘了这个样式设置是为了解决什么问题？？ */
  }

  span.custom-label i {
    margin: 0 3px;
  }

  /* 隐藏Chrome浏览器中el-input数字输入框右侧的上下调整小箭头 */
  ::v-deep .hide-spin-button input::-webkit-outer-spin-button,
  ::v-deep .hide-spin-button input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /* 隐藏Firefox浏览器中el-input数字输入框右侧的上下调整小箭头 */
  ::v-deep .hide-spin-button input[type="number"] {
    -moz-appearance: textfield;
  }
}

.required ::v-deep .el-form-item__label::before {
  content: "*";
  color: red;
  margin-right: 4px;
}

.static-content-item {
  min-height: 20px;
  display: flex; /* 垂直居中 */
  align-items: center; /* 垂直居中 */

  ::v-deep .el-divider--horizontal {
    margin: 0;
  }
}

.el-form-item.selected,
.static-content-item.selected {
  outline: 2px solid #409eff;
}

::v-deep .label-left-align .el-form-item__label {
  text-align: left;
}

::v-deep .label-center-align .el-form-item__label {
  text-align: center;
}

::v-deep .label-right-align .el-form-item__label {
  text-align: right;
}
</style>
