<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div style="height: 50px"></div>
  </view-wrapper>
</template>

<script>
import i18n, { translate } from '@/utils/i18n';
import viewWrapper from './view-wrapper';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import LtTableColumnEdit from '../../../lt-table-column-edit/lt-table-column-edit.vue';
import { executeInterface } from '@/api/interfaces/interfaces';

export default {
  name: 'data-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    LtTableColumnEdit,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: null,
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      currentPage: 1,
      size: 10,
      total: 0,
      loading: false,
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    // 将cols配置复制到组件中
  },
  mounted() {
    this.handleOnMounted();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },
  beforeDestroy() {
    this.handleOnBeforeDestroy()
    this.unregisterFromRefList();
  },
  methods: {
    sendApi() {
      try {
        if (this.field.options.api) {
          // 获取参数配置
          let param = this.getRequestParam();

          this.loading = true;

          executeInterface({
            apiId: this.field.options.api,
            body: param,
          })
            .then((res) => {
              this.loading = false;
              this.setValue(res.data)
              this.$nextTick(() => {
                if (this.field.options.onSuccessCallback) {
                  let customFunc = new Function(
                    'res',
                    this.field.options.onSuccessCallback,
                  );
                  customFunc.call(this, res);
                }
              });
            })
            .catch((err) => {
              this.loading = false;
              if (this.field.options.onFailCallback) {
                let customFunc = new Function(
                  'e',
                  this.field.options.onFailCallback,
                );
                customFunc.call(this, err);
              }
            });
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
  },
};
</script>
