<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
  > 


    <el-steps
       v-if="field.options.steps.length>1"
      :active="fieldModel"
      :direction="field.options.direction"
      :simple="field.options.simple"
      :align-center="field.options.alignCenter"
      :space="field.options.space ? +field.options.space : ''"
      :finish-status="field.options.finishStatus"
      :process-status="field.options.processStatus"
    >
      
      <el-step
        v-for="(item, index) in field.options.steps"
        :key="index"
        :title="item.title"
        :description="item.description"
      > 
        <template #icon=""  >
          <template v-if="item.icon">

            <i
            v-if="item.icon.includes('el-icon')"
            :style="{ color: item.color }"
            :class="item.icon"
          ></i>
          <svg-icon
            v-else
            :icon-class="item.icon"
            :style="{ color: item.color }"
          />
          </template>
          
        </template>
      </el-step>
     
    </el-steps>
     <lt-empty v-else></lt-empty>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
export default {
  name: "steps-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: 0,
      rules: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    this.handleOnMounted();
    console.log(this.field.options)
  },
  watch:{
    fieldModel:{
      handler(n,o){
        if (this.field.options.onChange) {
        let customFn = new Function('value','oldValue',this.field.options.onChange);
        customFn.call(this,n,o);
        }
      },
      immediate:true
      
    }
  },
  methods: {
    advance(){
      if(this.fieldModel<this.field.options.steps.length){
        this.fieldModel++
      }
    },
    back(){
      if(this.fieldModel>0){
         this.fieldModel--
      }
    }
  },
};
</script>

<style scoped>
::v-deep .is-simple .el-step__head{
  height: 20px;
}
</style>
