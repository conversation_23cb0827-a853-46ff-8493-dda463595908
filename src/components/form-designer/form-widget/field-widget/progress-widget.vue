<template>
  <view-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList">
    <el-progress :percentage="fieldModel" :type="field.options.progressType" :stroke-width="+field.options.strokeWidth"
      :width="+field.options.LineWidth" :text-inside="field.options.progressTextInside"
      :status="field.options.progressStatus" :show-text="field.options.progressShowText"
      :color="field.options.progressColor" :format="progressFormat" />
  </view-wrapper>
</template>
<!-- :text-color="field.options.progressTextColor"
      :define-back-color="field.options.progressDefineBackColor" -->
<script>
import viewWrapper from './view-wrapper';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import { executeInterface } from '@/api/interfaces/interfaces';
export default {
  name: 'progress-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: 0,
      rules: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    console.log(this.field.options.progressShowText);
    console.log(this.fieldModel)
  },
  computed: {
    textColor() {
      return this.field.options.progressTextColor
    },
  },
  mounted() {
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    // 进度条文字内容
    progressFormat(percentage) {
      if (this.field.options.onProgressFormat) {
        let JS = new Function("percentage", this.field.options.onProgressFormat)
        console.log(JS.call(this, percentage))
        return JS.call(this, percentage)
      }
      return `${percentage}%`;
    },
  },
};
</script>

<style scoped></style>
