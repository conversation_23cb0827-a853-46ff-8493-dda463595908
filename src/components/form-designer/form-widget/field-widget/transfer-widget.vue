<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="[]"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-transfer
      style="width: 100%; display: flex; align-items: center"
      :style="{ height: field.options.height && field.options.height + 'px' }"
      v-model="fieldModel"
      :key="custKey"
      :filterable="field.options.filterable"
      :filter-placeholder="field.options.placeholder"
      :titles="[
        field.options.title || '数据源',
        field.options.subtitle || '目标',
      ]"
      :props="{
        key: field.options.selectValue || 'value',
        label: field.options.selectLabel || 'label',
      }"
      :data="dataSource"
      :target-order="field.options.targetOrder"
      :left-default-checked="leftChecked"
      :right-default-checked="rightChecked"
      @change="change"
    >
    </el-transfer>
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { executeInterface } from '@/api/interfaces/interfaces';

export default {
  name: 'transfer-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    viewWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      fieldModel: [],
      dataSource: [],
      leftChecked: [],
      rightChecked: [],
      custKey: Math.random(),
    };
  },
  created() {
    // this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
  },

  mounted() {
    this.handleOnMounted();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    sendApi(){
      try {
      if (this.field.options.api) {
        // 获取参数配置
        let param = this.getRequestParam();

        let baseParam = {
          pageNum: this.currentPage,
          pageSize: this.size,
          sort: this.sort,
        };
        this.loading = true;

        executeInterface({
          apiId: this.field.options.api,
          body: { ...baseParam, ...param },
        })
          .then((res) => {
            if (res.data) {
              this.dataSource =
                ('records' in res.data ? res.data.records : res.data) || [];
              if (res.data.data) {
                this.dataSource = res.data.data || [];
              }
            }

            this.$nextTick(() => {
              if (this.field.options.onSuccessCallback) {
                let customFunc = new Function(
                  'res',
                  this.field.options.onSuccessCallback,
                );
                customFunc.call(this, res);
              }
            });
            this.custKey = Math.random();
            this.loading = false;
          })
          .catch((err) => {
            this.loading = false;
            // this.$message.error(err.message);
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    } catch (error) {
      this.loading = false;
      console.log(error);
    }
    },
    setLeftChecked(arr) {
      this.leftChecked = arr;
    },
    setRightChecked(arr) {
      this.rightChecked = arr;
    },
    change(e) {
      let customFn = new Function(
        'value',
        'selectedField',
        'unSelectedField',
        this.field.options.onChange,
      );
      customFn.call(
        this,
        e,
        this.getSelectedFields(),
        this.getUnSelectedFields(),
      );
    },
    setData(arr) {
      this.dataSource = arr;
    },
    getSelectedFields() {
      return this.dataSource.filter((item) => {
        if (this.fieldModel && this.fieldModel.length) {
          return this.fieldModel.includes(
            item[this.field.options.selectValue || 'value'],
          );
        }
      });
    },
    getUnSelectedFields() {
      return this.dataSource.filter((item) => {
        if (this.fieldModel && this.fieldModel.length) {
          return !this.fieldModel.includes(
            item[this.field.options.selectValue || 'value'],
          );
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-transfer-panel {
  flex: 1;
}
</style>
