<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-carousel
      :height="field.options.height||'200px'"
      :interval="field.options.interval"
      :direction="field.options.direction"
      :autoplay="field.options.autoplay"
      :type="field.options.isCard?'card':''"
    >
      <el-carousel-item v-for="(item, index) in arr" :key="index">
        <img :src="item" class="img"/>
      </el-carousel-item>
    </el-carousel>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import { getFile } from "@/api/file/file.js";
export default {
  name: "carousel-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: '',
      rules: [],
      arr:[]
    };
  },
  watch:{
    fieldModel:{
      immediate:true,
      handler(val){
        if(val){
          this.setArr(val)
        }
      }
    }
      
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods:{
    async setArr(val){
      let url = this.$store.state.app.uploadUrl;
      let arr = Array.isArray(val) ? val : [val];
      let result=[]
      for (let i = 0; i < arr.length; i++) {
        let res = await getFile({ fileId: arr[i] });
        res.data.forEach((item) => {
          result.push(url + item.pathName)
        });
      }
      this.arr=result
    }
  }
};
</script>

<style scoped>
.img{
		width: 100%;
	  height: 100%;
	  object-fit: contain;
}
</style>
