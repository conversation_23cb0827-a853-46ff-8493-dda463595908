<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div
      v-if="arr.length"
      ref="print"
      id="codeConfig"
      :style="{
        display: 'flex',
        width: 'fit-content',
        flexDirection:
          field.options.direction == 'horizontal' ? 'row' : 'column',
        flexWrap: 'wrap',
      }"
    >
      <template v-for="(componentData, index) in arr">
        <div
          :key="index"
          class="canvas"
          :style="{
            width: canvasStyleData.width + 'px',
            height: canvasStyleData.height + 'px',
          }"
        >
          <ComponentWrapper
            v-for="(item, index) in componentData"
            :key="index"
            :config="item"
            :designState="false"
          />
        </div>
      </template>
    </div>

    <lt-empty v-else></lt-empty>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import { viewCodeManage } from "@/api/interfaces/interfaces";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import ComponentWrapper from "@/directive/dragPage/Editor/ComponentWrapper";
import QRCode from "qrcode";
export default {
  name: "codeConfig-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    ComponentWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: {},
      rules: [],
      arr: [],
      canvasStyleData: {},
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    if (this.field.options.codeId) {
      viewCodeManage(this.field.options.codeId).then((r) => {
        let res = JSON.parse(r.data.codeJson);
        this.arr = [res.componentData];
        this.canvasStyleData = res.canvasStyleData;
        this.setValue(this.fieldModel)
        this.handleOnMounted();
      });
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setValue(params) {
      this.fieldModel = params
      if (Array.isArray(params)) {
        let arr = this.arr[0];
        let res = [];
        params.forEach((obj) => {
          let data = JSON.parse(JSON.stringify(arr));
          data.forEach((item) => {
            if (item.component == "v-field" && obj[item.field]) {
              item.propValue = obj[item.field];
            } else if (item.component == "Picture" && obj[item.field]) {
              QRCode.toDataURL(obj[item.field], (err, url) => {
                if (!err) item.propValue = url;
              });
            } else if (item.component == "BarCode" && obj[item.field]) {
              item.propValue = obj[item.field];
            }
          });
          res.push(data);
        });
        this.arr = res;
      } else {
        this.arr[0] && this.arr[0].forEach((item) => {
          if (item.component == "v-field" && params [item.field]) {
             item.propValue = params[item.field];
          } else if (item.component == "Picture" && params[item.field]) {
            QRCode.toDataURL(params[item.field], (err, url) => {
              if (!err) item.propValue = url;
            });
          } else if (item.component == "BarCode" && params[item.field]) {
            item.propValue = params[item.field];
          }
        })
        this.arr=this.arr.slice(0,1)
      }
    },
    print() {
      if (this.field.options.codeId) {

        this.$print.print(this.$refs.print);
      }
    },
  },
};
</script>

<style scoped>
.canvas {
  background: transparent;
  position: relative;
}
</style>
