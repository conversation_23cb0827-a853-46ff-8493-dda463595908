<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-input
      ref="fieldEditor"
      v-model="fieldModelName"
      :disabled="true"
      :size="field.options.size"
      class="hide-spin-button"
      :show-password="field.options.showPassword"
      :placeholder="field.options.placeholder"
      :minlength="field.options.minLength"
      :maxlength="field.options.maxLength"
      :show-word-limit="field.options.showWordLimit"
      :prefix-icon="field.options.prefixIcon"
      :suffix-icon="field.options.suffixIcon"
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @input="handleInputCustomEvent"
      @change="handleChangeEvent"
    >
      <el-button
        slot="append"
        v-if="field.options.appendButton"
        :disabled="field.options.disabled || field.options.appendButtonDisabled"
        :class="field.options.buttonIcon"
        @click.native="emitAppendButtonClick"
      ></el-button>
    </el-input>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import { mapGetters } from 'vuex';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { getBydeptIdInfo } from '@/api/system/user';
export default {
  name: 'deptinput-widget',
  computed: {
    ...mapGetters(['user']),
  },
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: '',
      fieldModelName: '',
      rules: [],
    };
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    if ([undefined, null, ''].includes(this.fieldModel)) {
      this.setValue(this.$store.getters.user.dept.deptId);
      this.fieldModelName = this.$store.getters.user.dept.deptName;
    } else {
      getBydeptIdInfo(parseInt(this.fieldModel)).then((response) => {
        this.setValue(response.data.deptId);
        this.fieldModelName = response.data.deptName;
      });
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {},
};
</script>

<style lang="scss" scoped>

</style>
