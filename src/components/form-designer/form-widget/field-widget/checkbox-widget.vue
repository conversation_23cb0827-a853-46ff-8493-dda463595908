<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <el-checkbox v-if="field.options.showCheckAll" :indeterminate="isIndeterminate" v-model="checkAll"
      @change="handleCheckAllChange" style="display: flex; flex-wrap: wrap;">全选</el-checkbox>
    <el-checkbox-group ref="fieldEditor" v-model="checkboxModel" :disabled="field.options.disabled"
      :size="field.options.size" @change="handleChangeEvent" style="display: flex;flex-wrap: wrap;">
      <template v-if="!!field.options.buttonStyle">
        <el-checkbox-button v-for="(item, index) in field.options.optionItems" :key="index" :label="item.value"
          :disabled="item.disabled" :border="field.options.border" :api-id="field.options.api"
          :style="{ display: field.options.displayStyle }">{{ item.label }}</el-checkbox-button>
      </template>
      <template v-else>
        <el-checkbox v-for="(item, index) in field.options.optionItems" :key="index" :label="item.value"
          :disabled="item.disabled" :border="field.options.border" :api-id="field.options.api"
          :style="{ display: field.options.displayStyle }">{{ item.label }}</el-checkbox>
      </template>
    </el-checkbox-group>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from 'element-ui/lib/mixins/emitter'
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import { executeInterface } from '@/api/interfaces/interfaces'
export default {
  name: "checkbox-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    subEditFlag: {
      type: Object,
      default: null
    },
    echoFieldData: {
      type: Array,
      default: () => []
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      checkboxModel: [],
      rules: [],
      isIndeterminate: false,
      checkAll: false,
    }
  },
  watch: {
    'field.options.optionItems': {
      immediate: true,
      handler(val) {
        this.judgeAllCheck()
      },
      deep: true,
    }
  },
  computed: {

  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initOptionItems()
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()
    this.handleOnCreated()
    this.splitValues()
  },

  mounted() {
    this.handleOnMounted()
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },

  methods: {
    getLabels(){
        let arr=[]
        this.checkboxModel.forEach(item=>{
           arr.push(this.field.options.optionItems.find(e=>e.value==item).label)
        })
        return arr.join('、')
    },
    sendApi(){
      if (this.field.options.api) {
      executeInterface({
        apiId: this.field.options.api
      }).then(res => {
        this.field.options.optionItems = res.data
      })
    }
    },
    judgeAllCheck() {
      // 初始化加载数据，如果显示了全选就需要判断全选状态，所以这里去设置全选选项值
      let checkedCount = this.checkboxModel.length;
      this.checkAll = checkedCount === this.field.options.optionItems.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.field.options.optionItems.length;
    },
    handleCheckAllChange(val) {

      this.checkboxModel = val ? this.field.options.optionItems.map(e => e.value) : [];
      this.isIndeterminate = false;
      // 修改附属值，反写fieldModel
       this.handleChangeEvent(this.checkboxModel)
      this.initFieldModelList()

    },
    splitValues() {
      if (this.fieldModel && this.fieldModel.length > 0) {
        if (typeof this.fieldModel == "string") {
          this.checkboxModel = this.fieldModel.split(',');
        }
      } else {
        this.checkboxModel = []
      }
    },
  }
}
</script>

<style lang="scss" scoped></style>
