<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-input
      ref="fieldEditor"
      v-model="fieldModel"
      :disabled="field.options.disabled"
      :readonly="field.options.readonly"
      :size="field.options.size"
      :style="{ color: field.options.color }"
      :class="['hide-spin-button', field.options.color ? 'color' : '']"
      :type="inputType"
      :show-password="field.options.showPassword"
      :placeholder="field.options.placeholder || '请输入' + field.options.label"
      :clearable="field.options.clearable"
      :minlength="field.options.minLength"
      :maxlength="field.options.maxLength"
      :show-word-limit="field.options.showWordLimit"
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @input="handleInputCustomEvent"
      @change="handleChangeEvent"
    >
      <!-- <el-button
        slot="append"
        v-if="field.options.appendButton"
        :disabled="field.options.disabled || field.options.appendButtonDisabled"
        @click.native="emitAppendButtonClick"
      ></el-button> -->
      <el-button
        slot="prepend"
        :icon="field.options.prefixIcon"
        @click="handlePrependClick"
        v-if="field.options.prepend || field.options.prefixIcon"
        >{{ field.options.prepend }}</el-button
      >
      <el-button
        slot="append"
        :icon="field.options.suffixIcon"
        @click="handleAppendClick"
        v-if="field.options.append || field.options.suffixIcon"
      >
        {{ field.options.append }}
      </el-button>
    </el-input>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';

export default {
  name: 'input-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    itemObj: {
      type: Object,
      default: () => ({
        name: '111',
      }),
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      test: null,
    };
  },
  computed: {
    inputType() {
      return this.field.options.type;
    },
    color() {
      if (!!this.field.options.color) {
        return this.field.options.color;
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.initAsData();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    handlePrependClick() {
      if (this.field.options.onPrependClick) {
        let fun = new Function('', this.field.options.onPrependClick);
        fun.call(this);
      }
    },
    handleAppendClick() {
      if (this.field.options.onAppendClick) {
        let fun = new Function('', this.field.options.onAppendClick);
        fun.call(this);
      }
    },
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}
</style>
