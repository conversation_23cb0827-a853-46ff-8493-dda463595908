<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <lt-select-page2
      ref="fieldEditor"
      v-model="fieldModel"
      class="full-width-input"
      :disabled="field.options.disabled"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :filterable="field.options.filterable"
      :placeholder="
        field.options.placeholder || i18nt('render.hint.selectPlaceholder')
      "
      :api-id="field.options.api"
      select-label="label"
      select-value="value"
      search-key="value"
      :choice-id="fieldModel"
      :multiple="field.options.multiple"
      @blur="handleBlurCustomEvent"
      @change="handleChangeEvent"
    />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
export default {
  name: 'SelectPageWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      selectItem: null,
      rules: [],
      loadingCount: 0,
    };
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable && !!this.field.options.allowCreate
      );
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    if (
      this.field.type === 'select-page' &&
      this.field.options.multiple &&
      this.fieldModel != null &&
      this.fieldModel != '' &&
      this.fieldModel.length != 0
    ) {
      this.fieldModel = this.fieldModel.split(',');
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    sendApi() {
      if (this.fieldModel && this.fieldModel.length && this.$refs.fieldEditor) {
        this.$refs.fieldEditor.getSelectPage(this.fieldModel);
      }
    },
    getSelectItem() {
      return this.selectItem;
    },
  },
};
</script>

<style lang="scss" scoped>


.full-width-input {
  width: 100% !important;
}
</style>
