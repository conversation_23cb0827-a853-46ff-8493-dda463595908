<template>
  <static-content-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >
    <el-button
      ref="fieldEditor"
      :type="field.options.type"
      :size="field.options.size"
      :plain="field.options.plain"
      :round="field.options.round"
      :circle="field.options.circle"
      :disabled="field.options.disabled"
      :loading="btnLoading"
      @click.native="handleCilck"
      v-if="field.options.authority ? checkPermi([field.options.name]) : true"
    >
     <template v-if="field.options.icon && !btnLoading">
              <i
                v-if="field.options.icon.includes('el-icon')"
                :style="{ color: field.options.iconColor }"
                :class="field.options.icon"
              ></i>
              <svg-icon
                v-else
                :icon-class="field.options.icon"
                :style="{ color: field.options.iconColor, margin: 0 }"
              />
            </template>
      {{ fieldModel && fieldModel.definitionId ? '查看' : field.options.label }}
    </el-button>
    <el-dialog
      :visible.sync="field.options.btnDialogVisible"
      title="流程数据"
      center
      width="1400px"
      append-to-body
    >
      <el-scrollbar
        v-if="field.options.btnDialogVisible && fieldModel"
        class="scrollbar"
      >
        <operationFlowFromCopy
          :definition-id-prop="fieldModel.definitionId"
          :select-type-prop="1"
        />
      </el-scrollbar>
    </el-dialog>
  </static-content-wrapper>
</template>

<script>
import StaticContentWrapper from './static-content-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import operationFlowFromCopy from '@/views/tool/variantform/operationFlowFromCopy.vue';
import { throttle } from '@/utils/decorator';

export default {
  name: 'ButtonWidget',
  componentName: 'FieldWidget',
  components: {
    StaticContentWrapper,
    operationFlowFromCopy,
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fieldModel: null,
      btnLoading: false,
      rules: []
    };
  },
  computed: {},
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },


  methods: {
    setLoading(flag){
       this.btnLoading=flag
    },
    @throttle(500)
    handleCilck() {
      this.handleButtonWidgetClick();
    },

  },
};
</script>

<style lang="scss" scoped>
; //* static-content-wrapper已引入，还需要重复引入吗？ *//
.scrollbar {
  width: 100%;
  height: 600px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
</style>
