<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag"
  >
    <el-input
      ref="fieldEditor"
      v-model="fieldName"
      :size="field.options.size"
      class="hide-spin-button"
      :placeholder="field.options.placeholder"
      :minlength="field.options.minLength"
      :disabled="field.options.disabled"
      :maxlength="field.options.maxLength"
      @focus="openPopupEvent"
    />
    <lt-page-dailog
      ref="pageDialogRef"
      :title="'请选择' + field.options.label"
      :auto-close="true"
      :api="field.options.api"
      :model-key="field.options.popupSelect"
      :model-key-value="field.options.popupSelectValue"
      :column-data="field.options.optionItems"
      @inputValue="popupSelectValue"
      @input="input"
    />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from 'element-ui/lib/mixins/emitter'
import i18n, { translate } from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import { executeInterface } from '@/api/interfaces/interfaces'
import { deepClone } from '@/utils/util'
import bus from '@/magic-editor/scripts/bus'
export default {
  name: 'PopupSelectWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    name: '',
    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    subEditFlag: {
      type: Object,
      default: null
    },
    echoFieldData:{
        type: Array,
        default:()=>[]
    },

  },
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      fieldName: null,
      choiceFlag: null, //弹窗选择器中的选中flag 用于触发onchange事件
      rules: [],
      popupData: null, // 弹框选择的内容
    }
  },
  computed: {
    inputType() {
      if (this.field.options.type === 'number') {
        return 'text' // 当input的type设置为number时，如果输入非数字字符，则v-model拿到的值为空字符串，无法实现输入校验！故屏蔽之！！
      }
      return this.field.options.type
    }

  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()
    this.handleOnCreated()
  },

  mounted() {
    this.handleOnMounted()
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },

  methods: {
    input(e){
      this.fieldName=e
    },
    popupSelectValue(e) {
      this.fieldModel=e
      this.handleChangeEvent(e)
    },
    openPopupEvent() {
      let params=this.getRequestParam()
      this.$refs.pageDialogRef.open(params)
    }
  }
}
</script>

<style lang="scss" scoped>
  

  ::v-deep .el-input__validateIcon {
    display: none;
  }


</style>
