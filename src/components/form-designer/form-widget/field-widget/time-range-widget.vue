<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
                     :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <el-time-picker ref="fieldEditor" is-range v-model="processValue" class="full-width-input"
                    :disabled="field.options.disabled" :readonly="field.options.readonly"
                    :size="field.options.size"
                    :clearable="field.options.clearable" :editable="field.options.editable"
                    :format="field.options.format" value-format="HH:mm:ss"
                    :start-placeholder="field.options.startPlaceholder || i18nt('render.hint.startTimePlaceholder')"
                    :end-placeholder="field.options.endPlaceholder || i18nt('render.hint.endTimePlaceholder')"
                    @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent"
                    @change="handleChangeEvent">
    </el-time-picker>
  </form-item-wrapper>
</template>

<script>
  import FormItemWrapper from './form-item-wrapper'
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

  export default {
    name: "time-range-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
      subEditFlag: {
        type: Object,
        default: null
      },
      echoFieldData:{
        type: Array,
        default:()=>[]
    },

    },
    components: {
      FormItemWrapper,
    },
    inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
    data() {
      return {
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        rules: [],
        processValue: []
      }
    },
    watch: {
      fieldModel: {
        handler(val) {
          if (val != '' && val != null) {
            if (typeof val == "string") {
              this.processValue =  val.split(',')
            }
          }
          this.$forceUpdate()
        },
        deep: true,
        immediate: true
      },
      processValue: {
        handler(val) {
          if (val !== '' && val !== null) {
            this.fieldModel =  val.join()
            this.$forceUpdate()
          }
        },
        deep: true,
        immediate: true
      }
    },
    computed: {

    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.initFieldModel()
      this.registerToRefList()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {

    }
  }
</script>

<style lang="scss" scoped>
  

  .full-width-input {
    width: 100% !important;
  }

</style>
