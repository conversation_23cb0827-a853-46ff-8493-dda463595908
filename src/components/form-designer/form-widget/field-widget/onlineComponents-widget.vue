<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <!-- 在线组件 -->
    <div class="preview">
      <code-viewer v-if="onlineCode" :source="onlineCode" ref="codeRef" :key="codeKey" />
    </div>
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import { previewAssembly } from '@/api/interfaces/interfaces';

export default {
  name: 'onlineComponents-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Object,
      default: null,
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      rules: [],
      queryDefault: {},
      codeKey: '',
      onlineCode: '',
    };
  },
  watch: {
    'field.options.onlineRelevance'() {
      this.codeKey = Math.random();
      this.init();
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    this.init();
  },
  mounted() {
    this.handleOnMounted();
    this.$bus.$off('onHandleEmit')
    this.$bus.$on('onHandleEmit', this.onHandleEmit);
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    init() {
      if (this.field.options.onlineRelevance) {
        previewAssembly(this.field.options.onlineRelevance)
          .then((res) => {
            this.onlineCode = res.data.templateJson;
            this.codeKey = Math.random();
          })
          .catch((e) => {
            this.$message.error('获取组件失败');
          });
      }
    },
    getCodeWidget(name) {
      return new Promise((resolve, reject) => {
        let timer = setInterval(() => {
          console.log('在线组件循环');
          const dfs = (root) => {
            if (
              root?.$options?.name &&
              root.$options.name === name &&
              'onlineFlag' in root['_data']
            ) {
              clearInterval(timer);
              resolve(root);
            }
            root.$children && root.$children.forEach(dfs);
          };
          dfs(this.$refs.codeRef);
        }, 0);

        setTimeout(() => {
          clearInterval(timer);
          reject(
            '获取在线组件失败,请检查所传入的name与在线组件的name值是否一致,以及在线组件data中必须要有个属性为onlineFlag(值任意)',
          );
        }, 6000);
      });
    },
    onHandleEmit(e) {
      if (this.field.options.onHandleEmit) {
        let JS = new Function('e', this.field.options.onHandleEmit);
        JS.call(this, e);
      }
    },
  },
};
</script>

<style scoped></style>
