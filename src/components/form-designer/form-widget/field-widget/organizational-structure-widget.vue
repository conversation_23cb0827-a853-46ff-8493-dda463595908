<template>
  <view-wrapper :designer="designer"  :field="field"  :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
                     :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <div
      class="left "
      :style="{
      // marginRight: isOpen ? '30px' : '15px',
      ...cssVar,
    }"
    >
      <div class="searchBox">
        <el-input v-model.trim="searchKey" :placeholder="field.options.placeholder">
          <i
            slot="suffix"
            class="el-input__icon el-icon-search pointer"
            @click="$refs.deviceTree.filter(searchKey)"
          />
        </el-input>
      </div>
      <el-scrollbar v-loading="loading" class="scrollbar" :style="cssVar">
        <el-tree
          ref="deviceTree"
          v-bind="$attrs"
          :data="deptOptions"
          node-key="nodeId"
          :expand-on-click-node="false"
          highlight-current
          default-expand-all
          :default-checked-keys="defaultChecked"
          :filter-node-method="filterNode"
          :check-strictly="!field.options.checkStrictly"
          @check="handleCheckChange"
          v-on="$listeners"
          :show-checkbox="field.options.multiple"
          @node-click="click"
        >
          <!-- text-ellipsis -->
          <div
            slot-scope="{ node, data }"
            class="flex-1 font-14"
            :class="{ noMinimumLevel: node.data.noMinimumLevel }"
          >
            <i
              v-if="data.dataType == 1"
              class="el-icon-set-up"
              style="color: #409eff"
            />
            <i
              v-if="data.dataType == 2"
              class="el-icon-odometer"
              style="color: #67c23a"
            />
            <el-tooltip effect="dark" :content="node.label" placement="bottom">
              <span>{{ node.label }}</span>
            </el-tooltip>
          </div>
        </el-tree>
      </el-scrollbar>
<!--      <div-->
<!--        class="bd-rd d-flex a-center j-center pointer"-->
<!--        :style="{ right: isOpen ? '-20px' : '-5px' }"-->
<!--        @click="isOpen = !isOpen">-->
<!--        <i-->
<!--          class="el-icon-arrow-left"-->
<!--          style="color: #ffffff"-->
<!--          :class="{ clear: !isOpen }"-->
<!--        />-->
<!--      </div>-->
    </div>
   </view-wrapper>
</template>

<script>
  import viewWrapper from './view-wrapper'

  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import {treeselect} from "@/api/system/dept";
  import { executeInterface } from '@/api/interfaces/interfaces'
  export default {
    name: "organizational-structure-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
      subEditFlag: {
        type: Object,
        default: null
      },
      echoFieldData:{
        type: Array,
        default:()=>[]
    },

    },
    components: {
      viewWrapper,
    },
    data() {
      return {
        loading: true,
        isOpen: true, // 是否展开
        fieldModel: null,
        height: "calc(100vh - 220px)",
        expandArr: [],
        searchKey: '',
        // 部门树选项
        deptOptions: [],
        defaultProps: {
          children: "children",
          label: "label",
        },
        clickNode: null,
        checkNode:[],
        childs: [],
        defaultChecked: []

     }
    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initEventHandler()
      this.handleOnCreated()

    },

    mounted() {
      this.mountedWap()
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },
    computed: {
      cssVar() {
        // css中访问js的变量 https://www.cnblogs.com/coder--wang/p/15179784.html
        return {
          "--height": this.height,
        };
      },
    },
    watch: {
      'field.options.showNodeType': {
        handler(newName, oldName) {
          this.getTreeselect()
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      async mountedWap(){
        await this.getTreeselect();
        this.handleOnMounted()
      },
      showNodeTypeFilter(){
        var than = this
        const fun = function (arr) {
          let tree = arr.filter(e => {
            if (than.field.options.showNodeType.length > 0) {
              return than.field.options.showNodeType.indexOf(e.dataType) != -1
            }
            return true;
          })

          tree.forEach((item) => {
            if (item.children) {
              item.children = fun(item.children);
            }
            // 判定插入子集数据

            let chs = than.childs.filter(ch => ch[than.field.options.organizationalList.id] == item.id)
            if (chs.length > 0) {
              chs.forEach((ch,index) =>{
                if (item.children == null) {
                  item.children = []
                }
                if (than.field.options.organizationalList.isDefault &&
                  than.fieldModel ==  null) {
                  than.defaultChecked.push(item.nodeId+ '-' + index)
                  than.fieldModel = {
                    children: [],
                    dataType: 0,
                    ch: ch,
                    id: ch[than.field.options.organizationalList.id],
                    isDisabled: false,
                    isShow: true,
                    label: ch[than.field.options.organizationalList.label],
                    nodeId: item.nodeId + '-' + index
                  }
                  than.clickNode =  than.fieldModel
                  than.checkNode = [ than.fieldModel]
                }
                item.children.push({
                  children: [],
                  dataType: 0,
                  ch: ch,
                  id: ch[than.field.options.organizationalList.id],
                  isDisabled: false,
                  isShow: true,
                  label: ch[than.field.options.organizationalList.label],
                  nodeId: item.nodeId+ '-' + index
                })
              })


            }
          });
          return tree
        };
          than.defaultChecked=than.defaultChecked.slice(0,1)

        this.deptOptions = fun(this.deptOptions)

      },
      /**
       * 筛选节点
       * @param {Object} value
       * @param {Object} data
       */
      filterNode(value, data) {
        if (!value) return true;
        return data.label.includes(value);
      },
      /** 查询部门下拉树结构 */
      async getTreeselect() {
        this.loading = true;
        if (this.field.options.organizationalList && this.field.options.organizationalList.api &&
          this.field.options.organizationalList.id &&
          this.field.options.organizationalList.label) {
          await executeInterface({
            apiId: this.field.options.organizationalList.api
          }).then(res => {
            this.childs = res.data

          })
        }
        await treeselect().then((response) => {
          this.deptOptions = response.data || {};
          this.showNodeTypeFilter()
          this.expandArr = this.getExpand();
          this.loading = false;
          console.log(this.deptOptions)
        }) .catch(() => {
          this.loading = false;
        });
      },
      click(e){
        this.clickNode = e
        this.fieldModel = e
        if (this.field.options.onNodeClick) {
          let changeFn = new Function('node',this.field.options.onNodeClick)
          changeFn.call(this, e)
        }
      },
      handleCheckChange(checkedNodes, checkedKeys) {
        this.checkNode = checkedKeys.checkedNodes
        this.fieldModel = checkedKeys.checkedNodes
        if (this.field.options.onNodeCheck) {
          let changeFn = new Function( 'node', 'nodes',this.field.options.onNodeCheck)
          changeFn.call(this, checkedNodes,checkedKeys.checkedNodes)

        }
      },
      getClickNode(){
        return this.clickNode
      },
      getCheckNodes(){
        return this.checkNode
      }
    }

  }
</script>

<style lang="scss" scoped>
  .scrollbar {
    width: 100%;
    height: var(--height);
  }
  ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 3px;
  }
  // ::v-deep .el-scrollbar__wrap {
  //   overflow-x: hidden !important;
  // }
  ::v-deep .el-scrollbar__wrap {
    overflow-x: auto;
    height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
  }
  ::v-deep .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
    white-space: nowrap;
    display: inline-block;
  }

  .el-tree {
    border-radius: 6px;
    padding: 5px;
    min-height: var(--height);
  }

  .left {
    // width: 200px;
    position: relative;
    // margin-right: 20px;
    transition: 0.5s;
  }

  .bd-rd {
    border-radius: 0 50px 50px 0;
    width: 20px;
    height: 40px;
    background-color: #e6ebf5;
    position: absolute;
    // top: 50%;
    // transform: translateY(-50%);
    top: calc(var(--height) / 2);
  }

  .clear {
    transform: rotate(180deg); //旋转180°
  }

  ::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
    color: #4d95fd;
    font-weight: bold;
    // width: 234px;
  }
  ::v-deep .el-tree-node__content > .el-checkbox {
    margin-right: 5px;
  }
  ::v-deep .searchBox .el-input {
    width: 90%;
    margin-left: 5%;
    overflow: hidden;
  }
  ::v-deep .searchBox .el-input--medium .el-input__inner {
    height: 30px;
    line-height: 30px;
  }
  ::v-deep .searchBox .el-input__suffix {
    top: -2px;
  }
  .node-tree-node__expand-icon {
    position: absolute;
    right: 10px;
    font-size: 12px;
  }
</style>
