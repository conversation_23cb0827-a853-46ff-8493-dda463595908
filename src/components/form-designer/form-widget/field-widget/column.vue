<template>
  <el-table-column
    :key="col.id"
    :width="col.width"
    :fixed="col.fix == 'left' ? 'left' : col.fix == 'right' ? 'right' : false"
    v-if="!col.children || col.children.length == 0"
    :label="col.label"
    :prop="col.prop"
    :align="col.align"
    :sortable="col.sortable ? 'custom' : false"
    :show-overflow-tooltip="col.isEdit ? false : col.overflow"
  >
    <template slot="header">
      <template v-if="col.icon">
        <i v-if="col.icon.includes('el-icon')" :class="col.icon"></i>
        <svg-icon v-else :icon-class="col.icon" />
      </template>
      {{ col.label }}
    </template>
    <template slot-scope="scope">
      <div
        v-if="col.code"
        v-html="
          codeComp(scope.row, scope.$index, col.code, scope.row[col.prop])
        "
      />
      <div v-else-if="col.colType">
        <div v-if="col.colType == 'user'" align="center">
          <div v-if="scope.row[col.prop]">
            <span>{{ userNames(scope.row[col.prop]) }}</span>
          </div>
        </div>
        <div v-if="col.colType == 'switch'">
          <div
            v-if="
              scope.row[col.prop] === 'true' || scope.row[col.prop] === 'false'
            "
          >
            <el-switch
              :disabled="field.options.disabled"
              :readonly="field.options.readonly"
              v-model="scope.row[col.prop]"
              @click.native.stop="switchClick(scope)"
              active-color="#13ce66"
              active-value="true"
              inactive-value="false"
            />
          </div>
          <div
            v-else-if="
              scope.row[col.prop] === '1' || scope.row[col.prop] === '0'
            "
          >
            <el-switch
              :disabled="field.options.disabled"
              :readonly="field.options.readonly"
              v-model="scope.row[col.prop]"
              @click.native.stop="switchClick(scope)"
              active-color="#13ce66"
              active-value="1"
              inactive-value="0"
            />
          </div>
          <div
            v-else-if="scope.row[col.prop] === 1 || scope.row[col.prop] === 0"
          >
            <el-switch
              :disabled="field.options.disabled"
              :readonly="field.options.readonly"
              v-model="scope.row[col.prop]"
              @click.native.stop="switchClick(scope)"
              active-color="#13ce66"
              :active-value="switchTypeValue[typeof scope.row[col.prop]][1]"
              :inactive-value="switchTypeValue[typeof scope.row[col.prop]][0]"
            />
          </div>
          <div v-else>
            <el-switch
              :disabled="field.options.disabled"
              :readonly="field.options.readonly"
              v-model="scope.row[col.prop]"
              @click.native.stop="switchClick(scope)"
              active-color="#13ce66"
              :active-value="true"
              :inactive-value="false"
            />
          </div>
        </div>
        <div v-if="col.colType == 'link'">
          <el-link type="primary" @click.native.stop="switchClick(scope)">{{
            scope.row[col.prop]
          }}</el-link>
        </div>
        <div v-if="col.colType == 'tag'">
          <el-tag :type="tagType(scope.row[col.prop]).type">{{
              tagType(scope.row[col.prop]).label
          }}</el-tag>
        </div>
        <div v-if="col.colType == 'time'">
          <span>{{ format(scope.row[col.prop]) }} </span>
        </div>
      </div>
      <template v-else>
        <template v-if="isShowEdit(scope.row)">
          <el-input
            v-if="hasColType(col, scope.row, 'input')"
            style="width: 100% !important"
            v-model="scope.row[col.prop]"
            @input="inputChange($event, scope)"
            @blur="blurChange($event, scope)"
            @focus="focusChange($event, scope)"
            :placeholder="'请输入' + col.label"
          />
          <el-date-picker
            v-else-if="hasColType(col, scope.row, 'datetime')"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            v-model="scope.row[col.prop]"
            @change="inputChange($event, scope)"
            @blur="blurChange($event, scope)"
            @focus="focusChange($event, scope)"
            :placeholder="'请选择' + col.label"
          />
          <el-date-picker
            v-else-if="hasColType(col, scope.row, 'date')"
            value-format="yyyy-MM-dd"
            type="date"
            v-model="scope.row[col.prop]"
            @change="inputChange($event, scope)"
            @blur="blurChange($event, scope)"
            @focus="focusChange($event, scope)"
            :placeholder="'请选择' + col.label"
          />
          <el-input
            v-else-if="hasColType(col, scope.row, 'number')"
            type="number"
            v-model="scope.row[col.prop]"
            @input="inputChange($event, scope)"
            @blur="blurChange($event, scope)"
            @focus="focusChange($event, scope)"
            :placeholder="'请输入' + col.label"
          />
          <el-select
            v-else-if="hasColType(col, scope.row, 'select')"
            v-model="scope.row[col.prop]"
            :placeholder="'请选择' + col.label"
            clearable
            filterable
            :allow-create="getColConfig(col, scope.row, 'select').allowCreate"
            @change="selectChange($event, scope)"
            @blur="blurChange($event, scope)"
            @focus="focusChange($event, scope)"
          >
            <el-option
              v-for="item in getColOptions(col, scope.row, 'select')"
              :label="
                item[
                  getColConfig(col, scope.row, 'select').selectLabel || 'label'
                ]
              "
              :value="
                item[
                  getColConfig(col, scope.row, 'select').selectValue || 'value'
                ]
              "
              :key="
                item[
                  getColConfig(col, scope.row, 'select').selectValue || 'value'
                ]
              "
            />
          </el-select>
          <el-switch
            v-else-if="hasColType(col, scope.row, 'switch')"
            v-model="scope.row[col.prop]"
            @change="inputChange($event, scope)"
            active-color="#13ce66"
            :active-value="true"
            :inactive-value="false"
          />
        </template>
        <span v-else>{{ getPreviewText(scope.row, col) }}</span>
      </template>
    </template>
  </el-table-column>
  <el-table-column
    v-else
    :key="col.id + 1"
    :width="col.width"
    :fixed="col.fix == 'left' ? 'left' : col.fix == 'right' ? 'right' : false"
    :label="col.label"
    :align="col.align"
    :sortable="col.sortable ? 'custom' : false"
  >
    <Column
      v-for="(subCol, index) in col.children"
      :key="subCol.id"
      :col="subCol"
      :hasChildren="true"
      :selectData="selectData"
      :index="index"
      :field="field"
      :disableRelation="disableRelation"
    ></Column>
  </el-table-column>
</template>

<script>
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
export default {
  name: 'Column',
  mixins: [emitter, fieldMixin, i18n],
  props: {
    col: {
      type: Object,
    },
    index: {
      type: Number,
    },
    field: {
      type: Object,
      default: () => {},
    },
    dictMap: {
      type: Object,
    },
    userMap: {
      type: Object,
      default: null,
    },
    selectData: {
      type: Object,
    },
    hasChildren: {
      type: Boolean,
      default: false,
    },
    disableRelation: {
      type: Object,
    },
  },
  data() {
    return {
      switchTypeValue: {
        number: [0, 1], // string类型的bool不太好判断
      },
    };
  },
  computed: {
    selectList() {
      let prop = this.col?.prop;
      let api = this.col?.selectConfig?.api;
      let optionItems = this.col?.selectConfig?.options?.optionItems;

      if (api) return this.selectData[`${prop}-${api}`];
      if (optionItems) return optionItems;
      return [];
    },
  },
  methods: {
    // 判断每一行的列的类型
    hasColType(col, row, type) {
      if (col.editType === 'custom') {
        return row['_' + col.prop + '_config'].type === type;
      } else {
        return col.editType === type;
      }
    },
    getColConfig(col, row, type) {
      if (col.editType === 'custom') {
        return row['_' + col.prop + '_config'];
      } else {
        return col.selectConfig;
      }
    },
    getColOptions(col, row, type) {
      if (col.editType === 'custom') {
        return row['_' + col.prop + '_config'].options.optionItems;
      } else {
        return this.selectList;
      }
    },
    userNames(userIds) {
      try {
        userIds = userIds + '';
        return userIds
          .split(',')
          .map((userId) => {
            return this.userMap[userId] ? this.userMap[userId].nickName : '';
          })
          .join();
      } catch (error) {
        return '';
      }
    },
    format(val) {
      if (val) {
        return val.replace('T', ' ');
      }
      return '';
    },
    tagType(value) {
      if (
        this.dictMap &&
        this.col.prop in this.dictMap &&
        value in this.dictMap[this.col.prop]
      ) {
        return {'value' : value, type: this.dictMap[this.col.prop][value], 'label': value};
      }
      if (this.col.tagTypes && this.col.tagTypes.find(e => e.value == value)) {
        let t = this.col.tagTypes.find(e => e.value == value);
        if (!t.label || t.label  == '') {
          t.label = value
        }
        return t;
      }
      return {'value' : value, type: 'default', 'label': value}
    },

    codeComp(row, $index, code, text) {
      return new Function('row', '$index', 'text', code)(row, $index, text);
    },
    switchClick(scope) {
      if (this.col.clickCode) {
        let JS = new Function('row', '$index', this.col.clickCode);
        JS.call(this, scope.row, scope.$index);
      }
    },
    inputChange(e, scope, extra = {}) {
      if (this.field.options.onEditChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onEditChange,
        );
        JS.call(
          this,
          e,
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    blurChange(e, scope, extra = {}) {
      if (this.field.options.onBlurChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onBlurChange,
        );
        JS.call(
          this,
          e,
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    focusChange(e, scope, extra = {}) {
      if (this.field.options.onFocusChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onFocusChange,
        );
        JS.call(
          this,
          e,
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    getPreviewText(row, col) {
      if (col.editType === 'select' && this.selectList) {
        let obj = this.selectList.find(
          (item) => item[col.selectConfig.selectValue] == row[col.prop],
        );
        if (obj) return obj[col.selectConfig.selectLabel];
      }
      return row[col.prop];
    },
    selectChange(e, scope) {
      let extra = {
        active: this.selectList.find(
          (item) =>
            item[this.col.selectConfig.selectValue] == scope.row[this.col.prop],
        ),
        selectList: this.selectList,
      };
      this.inputChange(e, scope, extra);
    },
    isShowEdit(row) {
      const { isEdit } = row;
      const { editable, prop } = this.col;
      let rowKey = this?.field?.options?.rowKey;
      let bol = isEdit && editable;

      if (bol && rowKey) {
        let primaryKey = row[rowKey];
        let temp = this.disableRelation[primaryKey];
        return this.disableRelation && primaryKey && temp && prop in temp
          ? !temp[prop]
          : true;
      }
      return isEdit && editable;
    },
  },
};
</script>

<style scoped>
::v-deep .el-input__inner {
  width: 100% !important;
  border: 0px;
}
</style>
