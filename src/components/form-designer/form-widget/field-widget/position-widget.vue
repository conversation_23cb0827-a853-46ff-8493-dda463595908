<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-amap
      ref="map"
      class="position"
      :style="{ height: '100px', width: '100px' }"
      vid="amapDemo"
      :amap-manager="amapManager"
      :center="center"
      :zoom="zoom"
      :plugin="plugin"
    >
    </el-amap>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import VueAMap from "vue-amap";
let amapManager = new VueAMap.AMapManager();
export default {
  name: "position-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    let self = this;
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      position: [],
      amapManager,
      zoom: 12,
      center: [121.59996, 31.197646],
      events: {},
      plugin: [
        {
          pName: "Geolocation",
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                console.log(status,result); //  能获取定位的所有信息
                if (result && result.position) {
                  self.center = result.position;
                  self.position = result.position;
                }
              });
            },
          },
        },
      ],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    getPosition() {
      return this.position;
    },
  },
};
</script>

<style>
.position .amap-controls{
  display: none;
}
</style>
