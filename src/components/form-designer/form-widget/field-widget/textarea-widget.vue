<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-input
      type="textarea"
      ref="fieldEditor"
      v-model="fieldModel"
      :disabled="field.options.disabled"
      :readonly="field.options.readonly"
      :size="field.options.size"
      :placeholder="field.options.placeholder || '请输入' + field.options.label"
      :rows="field.options.rows"
      :minlength="field.options.minLength"
      :maxlength="field.options.maxLength"
      :show-word-limit="field.options.showWordLimit"
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @input="handleInputCustomEvent"
      @change="handleChangeEvent"
    >
    </el-input>
    <div v-if="field.options.showReply" class="mr-t10 d-flex flex-wrap">
      <el-tag
        type="info"
        style="margin: 0 5px 5px 0; cursor: pointer"
        size="medium"
        :closable="
          !field.options.defaultReply.includes(item) && field.options.custom
        "
        @close="close(item)"
        @click.native="setValue(item)"
        v-for="item in replyList"
        :key="item"
        >{{ item.length > 8 ? item.slice(0, 8) + "..." : item }}</el-tag
      >
      <i
        v-if="field.options.custom"
        class="el-icon-circle-plus-outline"
        @click="addReply"
        style="font-size: 22px; margin: 3px 0 0 10px; cursor: pointer"
      ></i>
    </div>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from "./form-item-wrapper";
import emitter from "element-ui/lib/mixins/emitter";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

export default {
  name: "textarea-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      replyList: [],
    };
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    if (this.field.options.showReply) {
      this.getReply();
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    close(item) {
      this.$confirm("是否确认删除此回复", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let arr = localStorage.getItem(
          "replyList" + this.$store.state.user.userId
        )
          ? JSON.parse(
              localStorage.getItem("replyList" + this.$store.state.user.userId)
            )
          : [];
        arr = arr.filter((e) => e !== item);
        localStorage.setItem(
          "replyList" + this.$store.state.user.userId,
          JSON.stringify(arr)
        );
        this.$message({
          type: "success",
          message: "删除成功",
        });
        this.getReply();
      });
    },
    addReply() {
      this.$prompt("请输入回复内容", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          if (!value) {
            this.$message({
              type: "warning",
              message: "回复内容不能为空",
            });
            return;
          }
          let arr = localStorage.getItem(
            "replyList" + this.$store.state.user.userId
          )
            ? JSON.parse(
                localStorage.getItem(
                  "replyList" + this.$store.state.user.userId
                )
              )
            : [];

          arr.push(value);
          localStorage.setItem(
            "replyList" + this.$store.state.user.userId,
            JSON.stringify(arr)
          );

          this.getReply();
          this.$message({
            type: "success",
            message: "添加成功",
          });
        })
    },
    getReply() {
      if (!this.field.options.defaultReply) {
        this.$set(this.field.options, "defaultReply", []);
      }
      let arr = localStorage.getItem(
        "replyList" + this.$store.state.user.userId
      )
        ? JSON.parse(
            localStorage.getItem("replyList" + this.$store.state.user.userId)
          )
        : [];
      this.replyList = [
        ...new Set(this.field.options.defaultReply.concat(arr)),
      ];
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
