<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div>
      <div class="d-flex no-print j-center titleBox">
        <div
          class="d-flex a-center leftBox"
          v-if="field.options.leftBtns && field.options.leftBtns.length"
        >
          <template v-for="(item, index) in field.options.leftBtns">
            <el-link
              :underline="false"
              :key="index"
              :type="item.type"
              @click="handleLeftClick(item.fun)"
              v-if="
                handleLeftDisplay(item.displayFun) &&
                (field.options.authority
                  ? checkPermi([
                      `${field.options.name}-leftBtn-${item['cust-id']}`,
                    ])
                  : true)
              "
            >
              <template v-if="item.icon">
                <i
                  v-if="item.icon && item.icon.includes('el-icon')"
                  :class="item.icon"
                ></i>
                <svg-icon
                  :class="[
                    !!!item.color && item.type ? 'el-button--' + item.type : '',
                  ]"
                  v-else
                  :icon-class="item.icon"
                />
              </template>
              {{ item.name }}
            </el-link>
          </template>
        </div>
        <div style="font-weight: 600" v-if="!field.options.labelHidden">
          {{ field.options.label }}
        </div>
        <div class="rightBox">
          <template>
            <el-button
              type="primary"
              v-if="isShowPrint && !total"
              icon="el-icon-printer"
              @click="doPrint()"
            >
              打印
            </el-button>
            <el-dropdown
              trigger="click"
              v-else-if="isShowPrint && total"
              @command="doPrint"
            >
              <el-button type="primary" icon="el-icon-printer">
                打印
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="page">当前页</el-dropdown-item>
                <el-dropdown-item divided command="all" style="color: #ffbf40"
                  >全部</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>

            <el-button
              type="primary"
              icon="el-icon-download"
              class="mr-l5"
              @click="doExport()"
              v-if="isShowExport && !total"
            >
              导出
            </el-button>

            <el-dropdown
              v-else-if="isShowExport && total"
              trigger="click"
              @command="doExport"
            >
              <el-button type="primary" icon="el-icon-download" class="mr-l5">
                导出
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="page">当前页</el-dropdown-item>
                <el-dropdown-item divided command="all" style="color: #ffbf40"
                  >全部</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </template>

          <el-popover placement="bottom-start" trigger="click">
            <lt-sort-table
              v-model="copyCol"
              isEnable
              row-key="id"
              @change="colChange"
            >
              <el-table-column label="名称" prop="label" />
              <el-table-column label="隐藏" prop="hidden">
                <template slot-scope="{ row }">
                  <el-switch
                    v-model="row.hidden"
                    @change="colChange"
                  ></el-switch>
                </template>
              </el-table-column>
            </lt-sort-table>
            <el-button
              type="primary"
              slot="reference"
              class="mr-l5"
              icon="el-icon-setting"
              v-if="field.options.isShowTableField"
              >字段管理</el-button
            >
          </el-popover>
        </div>
      </div>
      <div class="pd-t10">
        <lt-sort-table
          :id="`data-table-${stringToIntHash(field.id)}`"
          :class="{
            'hide-checkbox': field.options.selectionLimit,
          }"
          v-loading="loading"
          v-model="copyData"
          ref="sortTableRef"
          v-if="flag"
          :height="
            computedHeight ||
            (field.options.minHeight ? field.options.minHeight + 'px' : 'auto')
          "
          :max-height="field.options.height || 'auto'"
          :highlight-current-row="field.options.highlightCurrentRow"
          :custKey="custKey"
          :lazy="field.options.isLazy"
          :load="lazyLoad"
          :expand-row-keys="expandRowKeys"
          :show-summary="field.options.showSummary"
          :summary-method="getSummaries"
          :row-key="getRowKey"
          :tree-props="{
            children: field.options.subsetField || 'children',
            hasChildren: 'hasChildren',
          }"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
          :border="field.options.hasFrame"
          :stripe="field.options.tableStripe"
          :isEnable="field.options.isEnable && !field.options.treeTable"
          :span-method="spanMethod"
          :header-cell-style="rowClass"
          :default-expand-all="field.options.defaultExpandAll"
          @row-click="onRowClick"
          @sort-change="sortChange"
          @current-change="currentChange"
          @cell-dblclick="cellDblclick"
          @cell-click="cellClick"
          @select="select"
          @select-all="selectAll"
          @change="change"
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
          @header-dragend="handleHeaderDragend"
        >
          <el-table-column
            v-if="field.options.hasSelection"
            :reserve-selection="true"
            type="selection"
            :selectable="checkStatus"
            width="50"
          >
          </el-table-column>
          <el-table-column
            type="expand"
            v-if="
              field.options.hasExpand &&
              field.widgetList &&
              field.widgetList.length > 0
            "
          >
            <template slot-scope="{ $index }">
              <div
                style="min-height: 100px; padding: 20px"
                :style="{ zIndex: zCIndex }"
              >
                <template v-for="(subWidget, swIdx) in field.widgetList">
                  <template v-if="'container' === subWidget.category">
                    <component
                      :is="subWidget.type + '-item'"
                      :widget="subWidget"
                      :key="swIdx"
                      :parent-list="field.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="field"
                    ></component>
                  </template>
                  <template v-else>
                    <component
                      :is="subWidget.type + '-widget'"
                      :field="subWidget"
                      :designer="null"
                      :key="swIdx"
                      :parent-list="field.widgetList"
                      :index-of-parent-list="swIdx"
                      :sub-form-row-index="$index"
                      :sub-form-row-id="$index"
                      :parent-widget="field"
                    ></component>
                  </template>
                </template>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="序号"
            v-if="field.options.showIndex"
            width="55"
          >
            <template slot-scope="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <template v-for="(col, index) in copyCol.filter((item) => item)">
            <Column
              v-if="!col.hidden"
              :col="col"
              :key="col.id"
              :index="index"
              :field="field"
              :dict-map="dictMap"
              :user-map="userMap"
              :selectData="selectData"
              :disableRelation="disableRelation"
            />
          </template>
          <el-table-column
            class-name="noExl"
            align="center"
            fixed="right"
            label="操作"
            v-if="
              field.options.btns.length > 0 &&
              (!field.options.disabled || !field.options.readonly)
            "
            :width="getOperColumnWidth()"
          >
            <template slot-scope="{ row, $index }">
              <template v-for="(btn, index) in field.options.btns">
                <span
                  :key="index"
                  v-if="
                    hasButDisplay(btn, row, index) &&
                    (field.options.authority
                      ? checkPermi([`${field.options.name}-btn-${index}`])
                      : true)
                  "
                >
                  <el-tooltip
                    v-if="btn.tip"
                    class="item"
                    effect="dark"
                    :content="btn.tip"
                    placement="top"
                  >
                    <table-cells
                      :btn="btn"
                      @click.native.stop="click(row, $index, btn.fun)"
                    />
                  </el-tooltip>
                  <table-cells
                    v-else
                    :btn="btn"
                    @click.native.stop="click(row, $index, btn.fun)"
                  />
                </span>
              </template>
            </template>
          </el-table-column>
        </lt-sort-table>
      </div>

      <div v-if="designState && field.options.hasExpand">
        <div
          style="min-height: 100px; padding: 20px"
          class="dialog-body-container field-action"
          @click.stop="selectWidget(field)"
        >
          <draggable
            style="border: 1px solid #409eff"
            :list="field.widgetList"
            v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
            handle=".drag-handler"
            @add="(evt) => onContainerDragAdd(evt, field.widgetList)"
            @update="onContainerDragUpdate"
            :move="checkContainerMove"
          >
            <transition-group
              name="fade"
              tag="div"
              class="form-widget-list"
              :style="{ height: field.widgetList.length ? 'auto' : '68px' }"
            >
              <template v-for="(subWidget, swIdx) in field.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component
                    :is="subWidget.type + '-widget'"
                    :widget="subWidget"
                    :designer="designer"
                    :key="subWidget.id"
                    :parent-list="field.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="field"
                  ></component>
                </template>
                <template v-else>
                  <component
                    :is="subWidget.type + '-widget'"
                    :field="subWidget"
                    :designer="designer"
                    :key="subWidget.id"
                    :parent-list="field.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="field"
                    :design-state="true"
                  ></component>
                </template>
              </template>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div style="height: auto; width: 100%">
        <lt-pagination
          :total="total"
          :page.sync="currentPage"
          :limit.sync="size"
          :pagerCount="Number(field.options.pagerCount) || 7"
          :auto="false"
          :pageSizes="
            pageSizes.length == 0 || !pageSizes ? [10, 20, 30, 50] : pageSizes
          "
          @pagination="loadData(false)"
          :layout="
            field.options.pageStyle && field.options.pageStyle instanceof Array
              ? field.options.pageStyle.join()
              : 'total, sizes, prev, pager, next, jumper'
          "
        />
      </div>
    </div>
  </view-wrapper>
</template>

<script>
import i18n, { translate } from "@/utils/i18n";
import viewWrapper from "./view-wrapper";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import {
  executeInterface,
  excel2PdfByTable,
} from "@/api/interfaces/interfaces";
import Column from "./column.vue";
import bus from "@/magic-editor/scripts/bus";
import { getUserAvatars } from "@/api/system/user";
import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin";
import ContainerItemWrapper from "@/components/form-render/container-item/container-item-wrapper";
import FieldComponents from "@/components/form-designer/form-widget/field-widget/index";
import Draggable from "vuedraggable";
import { colsUpdate } from "@/api/tool/form";
import TableCells from "../components/table-cells.vue";
const _popupManager = require("element-ui/lib/utils/popup/popup-manager");
export default {
  name: "data-table-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    Column,
    Draggable,
    ContainerItemWrapper,
    ...FieldComponents,
    TableCells,
  },
  computed: {
    isShowPrint() {
      const { options } = this.field;
      let permiKey = `${options.name}-print`;
      return (
        ("hasPrint" in options ? options.hasPrint : true) &&
        (options.authority ? this.checkPermi([permiKey]) : true)
      );
    },
    isShowExport() {
      const { options } = this.field;
      let permiKey = `${options.name}-export`;
      return (
        ("hasExport" in options ? options.hasExport : true) &&
        (options.authority ? this.checkPermi([permiKey]) : true)
      );
    },
    widthComputed() {
      const {
        copyData,
        field: {
          options: { btns, authority, name },
        },
      } = this;
      return {
        copyData,
        btns,
        authority,
        name,
      };
    },
    abbreviationShow() {
      return (
        this.copyData.length > 0 &&
        !this.isFieldList &&
        !this.field.options.subsetField
      );
    },
  },

  mixins: [emitter, fieldMixin, containerMixin, i18n],
  props: {
    field: Object,
    isFieldList: Boolean,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: null,
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  watch: {
    "field.options.api": {
      handler(val, oldVal) {
        this.loadData();
      },
      deep: true,
    },
    copyData: {
      handler(val, oldVal) {
        if (val != this.formModel) {
          // 数据模式下的处理
          // const { numberMode } = this.field.options
          // if (numberMode) {
          //   const { open, digit, round } = numberMode
          //   if (open) {
          //     if (this.field.options.treeTable) {
          //       let subsetField = this.field.options.subsetField || "children"
          //       const bfs = (root) => {
          //         root.forEach((item) => {
          //           // 遍历每一个列
          //           for (let key in item) {
          //             item[key] = this.handleNumber(item[key], digit, round)
          //           }
          //           item[subsetField] && bfs(item[subsetField])
          //         })
          //       }
          //       bfs(val)
          //     } else {
          //       val.forEach((item) => {
          //         // 遍历每一个列
          //         for (let key in item) {
          //           item[key] = this.handleNumber(item[key], digit, round)
          //         }
          //       })
          //     }
          //   }
          // }
          this.setValue(val);
        }
        if (this.field.options.heightFit) {
          this.resizeHeight();
        }
        this.$nextTick(() => {
          let userCol = this.copyCol.filter((e) => e.colType == "user");
          // 用户头像请求
          if (userCol.length) {
            let userIds = [];
            userCol.forEach((us) => {
              let colThis = [];
              this.copyData
                .filter((e) => e[us.prop])
                .forEach((e) => {
                  e[us.prop] = e[us.prop] + "";
                  if (e[us.prop].includes(",")) {
                    colThis.push(...e[us.prop].split(","));
                  } else {
                    colThis.push(e[us.prop]);
                  }
                });
              if (colThis) {
                userIds = [...userIds, ...colThis];
              }
            });
            if (userIds && userIds.length > 0) {
              getUserAvatars({ userIds: userIds }).then((data) => {
                this.userMap = data.data;
              });
            }
          }
        });
      },
      deep: true,
    },
    "field.options.cols": {
      handler(val, oldVal) {
        this.copyCol = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
    copyCol: {
      handler(n, o) {
        this.flag = false;
        this.$nextTick(() => {
          this.flag = true;
        });
      },
      deep: true,
    },
    widthComputed() {
      this.getOperColumnWidth();
    },
    // 不能删,删了的话lt-sort-table组件中的isEnable会失效
    "field.options.isEnable": function () {},
  },
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      pageSizes: [],
      currentPage: 1,
      size: 10,
      total: 0,
      flag: true,
      loading: false,
      copyCol: [],
      copyData: [],
      sort: { prop: "", order: "" },
      currentSelectData: [],
      dictMap: {}, // 字典
      userMap: {}, // 用户名称头像
      custKey: "", // 用于表格的刷新
      computedHeight: "",
      setCurrentSelectDataUnfinished: [], //初始化表格的时候设置选中内容的时候，由于数据表的数据还没有，则选中不了，将设置进入的加载到这里，等到数据加载完成在重新调用选中
      expandRowKeys: [],
      selectData: {}, // 下拉选项的数据源
      zCIndex: 2001,
      maskZIndex: 2001,
      disableRelation: {},
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    // 由于新增了widgetList 以前没有的在这里补全
    if (this.designState) {
      if (!this.field.widgetList) this.field["widgetList"] = [];
    }
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.loadData();
    }

    // 将 this.fieldModel 引用关系链接到copyData
    if (this.fieldModel) {
      this.loading = true;

      if (this.field.options.treeTable) {
        // 树表循环子集看是否需要转换未json 流程中保存树表问题 子集成了转义字符
        let tbData =
          typeof this.fieldModel == "string"
            ? JSON.parse(this.fieldModel)
            : this.fieldModel;
        let inter = (list) => {
          let tbData = typeof list == "string" ? JSON.parse(list) : list;
          for (let i = 0; i < tbData.length; i++) {
            if (tbData[i][this.field.options.subsetField]) {
              tbData[i][this.field.options.subsetField] = inter(
                tbData[i][this.field.options.subsetField]
              );
            }
          }
          return tbData;
        };
        this.copyData = inter(tbData);
      } else {
        this.copyData =
          typeof this.fieldModel == "string"
            ? JSON.parse(this.fieldModel)
            : this.fieldModel;
      }
      this.custKey = Math.random();
      setTimeout(() => {
        this.loading = false;
      }, 300);
    }
    bus.$on("showTableAbb", (flag) => {
      this.isShow = flag;
    });

    if (this.field.options.heightFit) {
      bus.$on("resizeHeight", this.resizeHeight);
      if (this.field.options.heightFit && !this.computedHeight) {
        this.resizeHeight();
      }
    } else {
      bus.$on("resizeHeight", () => {});
    }

    // select下拉选项数据源初始化
    if (this?.field?.options?.cols.length) {
      const bfs = (root) => {
        root.forEach((item) => {
          let selectConfig = item?.selectConfig;
          if (selectConfig && selectConfig.api) {
            // 请求
            executeInterface({
              apiId: selectConfig.api,
              body: {},
            })
              .then((res) => {
                this.$set(
                  this.selectData,
                  `${item.prop}-${selectConfig.api}`,
                  res.data
                );
                if (selectConfig.onSuccessCallback) {
                  let customFunc = new Function(
                    "res",
                    selectConfig.onSuccessCallback
                  );
                  customFunc.call(this, res);
                }
              })
              .catch((err) => {
                if (selectConfig.onFailCallback) {
                  let customFunc = new Function(
                    "e",
                    selectConfig.onFailCallback
                  );
                  customFunc.call(this, err);
                }
              });
          }
          item.children && bfs(item.children);
        });
      };

      bfs(this?.field?.options?.cols);
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
    bus.$off("resizeHeight");
  },
  methods: {
    getRowKey(row) {
      let rowKey = row[this.field.options.rowKey || "id"];

      if (rowKey == 0) {
        return rowKey + "";
      } else {
        return rowKey;
      }
    },
    rowClass({ row, column, rowIndex, columnIndex }) {
      //表头单元格的 style 的回调方法
      if (this.field.options.onRowClass) {
        let JS = new Function(
          "row",
          "column",
          "rowIndex",
          "columnIndex",
          this.field.options.onRowClass
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }
      return null;
    },
    change(oldData, data) {
      if (this.field.options.onSortChange) {
        let JS = new Function(
          "oldData",
          "data",
          this.field.options.onSortChange
        );
        return JS.call(this, oldData, data);
      }
    },
    setRowEditable(id, flag, callback) {
      let obj = null;
      let bfs = (arr) => {
        arr.forEach((item) => {
          if (item[this.field.options.rowKey] == id) {
            obj = item;
          }
          let children = item[this.field.options.subsetField || "children"];
          children && bfs(children);
        });
      };
      bfs(this.copyData);
      if (obj) {
        if (callback && callback(obj) === false) return;
        this.$set(obj, "isEdit", flag);
      }
    },
    setExpandRow(arr) {
      this.expandRowKeys = arr;
    },
    addSubFormFirstRow(subData) {
      this.addSubFormRow(subData, 0);
      // return new Promise((resolve, reject) => {
      //   resolve(this.rowIdData[0]);
      // });
    },
    addSubFormLastRow(subData) {
      let oldSubFormData = this.getData() || [];
      this.addSubFormRow(subData, oldSubFormData.length);
      // return new Promise((resolve, reject) => {
      //   resolve(this.getData().length);
      // });
    },
    delSubFormRow(formRowIndex) {
      let oldSubFormData = this.getData() || [];
      oldSubFormData.splice(
        formRowIndex == null ? oldSubFormData.length : formRowIndex,
        1
      );
      this.updateTableData(oldSubFormData);
    },
    addSubFormRow(subData, index) {
      let oldSubFormData = this.getData() || [];
      // 新增下标位置
      index = index == null ? oldSubFormData.length : index;
      oldSubFormData.splice(
        index == null ? oldSubFormData.length : index,
        0,
        subData
      );
      this.updateTableData(oldSubFormData);
      return new Promise((resolve, reject) => {
        resolve(index);
      });
    },
    setSubFormRow(index, data) {
      let oldSubFormData = this.getData() || [];
      // 判断列表长度
      if (index <= oldSubFormData.length - 1) {
        oldSubFormData.splice(
          index == null ? oldSubFormData.length : index,
          1,
          data
        );
        this.updateTableData(oldSubFormData);
      }

      return new Promise((resolve, reject) => {
        resolve(index);
      });
    },
    getSubTable(index, name) {
      if (!name) {
        name = this.field.widgetList[0].id;
      }
      let foundRef = this.refList[name + "@row" + index];
      if (!foundRef) {
        return null;
      }
      return foundRef;
    },
    setColHidden(prop, flag = true) {
      for (let i = 0; i < this.copyCol.length; i++) {
        if (this.copyCol[i].prop == prop) {
          this.copyCol[i]["hidden"] = flag;
        }
      }
    },
    /**
     * 操作列宽度的计算
     */
    getOperColumnWidth() {
      let arr = [];
      this.copyData.forEach((row, index) => {
        let btns = this.field.options.btns.filter((btn, btnIndex) => {
          return (
            this.hasButDisplay(btn, row, index) &&
            (this.field.options.authority
              ? this.checkPermi([`${this.field.options.name}-btn-${btnIndex}`])
              : true)
          );
        });
        if (btns && btns.length) {
          // 文字的宽度计算
          let temp = btns
            .filter((item) => item.type === 1 || !("type" in item))
            .map((item) => item.name.length);
          let width1 = temp.length ? temp.reduce((n, m) => n + m) * 22 : 0;
          // 图标的宽度计算
          let width2 = btns.filter((item) => item.type === 2).length * 40;
          arr.push(width1 + width2 + 20);
        } else {
          arr.push(25);
        }
      });
      let num = Math.max.apply(null, arr);
      return num > 60 ? num : 60;
    },
    // 合并行列事件
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.field.options.onSpanMethod) {
        let JS = new Function(
          "row",
          "column",
          "rowIndex",
          "columnIndex",
          this.field.options.onSpanMethod
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }
      // 默认一行一列
      return { rowspan: 1, colspan: 1 };
    },
    checkStatus(row) {
      if (!this.field.options.selectionLimit) {
        return true;
      }
      const state = this.currentSelectData.some(
        (item) =>
          item[this.field.options.rowKey || "id"] ===
          row[this.field.options.rowKey || "id"]
      );
      return (
        this.currentSelectData.length < this.field.options.selectionLimit ||
        state
      );
    },
    setPageSizes(pageSizes) {
      if (!Array.isArray(pageSizes)) {
        return;
      }
      this.pageSizes = pageSizes;
      this.size = pageSizes[0];
    },
    getData() {
      return this.copyData;
    },
    getCol() {
      return this.copyCol;
    },
    lazyLoad(tree, treeNode, resolve) {
      if (this.field.options.onLazy) {
        let js = new Function(
          "tree",
          "treeNode",
          "resolve",
          this.field.options.onLazy
        );
        return js.call(this, tree, treeNode, resolve);
      }
    },
    setDictMap(dictMap) {
      this.dictMap = dictMap;
    },
    // 行样式设置
    tableRowStyle({ row, rowIndex }) {
      if (this.field.options.onRowStyle) {
        let JS = new Function("row", "rowIndex", this.field.options.onRowStyle);
        return JS.call(this, row, rowIndex);
      }

      return null;
    },
    tableCellStyle({ row, column, rowIndex, columnIndex }) {
      if (this.field.options.onCellClick || this.field.options.onCellDblclick) {
        row.rowIndex = rowIndex;
        column.columnIndex = columnIndex;
      }
      if (this.field.options.onCellStyle) {
        let JS = new Function(
          "row",
          "column",
          "rowIndex",
          "columnIndex",
          this.field.options.onCellStyle
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }

      return null;
    },
    handleSelectionChange(val) {
      this.currentSelectData = val;
      if (this.field.options.onSelection) {
        let JS = new Function("val", this.field.options.onSelection);
        JS.call(this, val);
      }
    },
    setRowSelected() {
      this.$nextTick(() => {
        this.copyData.forEach((row) => {
          this.getTableRef() &&
            this.getTableRef().toggleRowSelection(row, false);
        });
      });
    },
    currentChange(currentRow, oldCurrentRow) {
      if (this.field.options.onCurrentChange) {
        let JS = new Function(
          "currentRow",
          "oldCurrentRow",
          this.field.options.onCurrentChange
        );
        JS.call(this, currentRow, oldCurrentRow);
      }
    },
    onRowClick(row, column) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onRowClick) {
        let JS = new Function("row", "column", this.field.options.onRowClick);
        JS.call(this, row, column);
      }
    },
    cellDblclick(row, column, cell, event) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onCellDblclick) {
        let JS = new Function(
          "row",
          "column",
          "cell",
          "event",
          "rowIndex",
          "columnIndex",
          this.field.options.onCellDblclick
        );
        JS.call(
          this,
          row,
          column,
          cell,
          event,
          row.rowIndex,
          column.columnIndex
        );
      }
    },
    cellClick(row, column, cell, event) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onCellClick) {
        let JS = new Function(
          "row",
          "column",
          "cell",
          "event",
          "rowIndex",
          "columnIndex",
          this.field.options.onCellClick
        );
        JS.call(
          this,
          row,
          column,
          cell,
          event,
          row.rowIndex,
          column.columnIndex
        );
      }
    },
    hasButDisplay(btn, row, index) {
      try {
        if (!btn.displayFun) {
          return true;
        } else {
          let cell = new Function("row", "index", btn.displayFun);
          return cell.call(this, row, index);
        }
      } catch (e) {
        // console.error(e, btn.displayFun, '函数执行失败');
        return true;
      }
    },
    doPrint(type) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onClickPrint) {
        let changeFn = new Function(this.field.options.onClickPrint);
        changeFn.call(this);
        return;
      }
      this.$print.print(this.getTableRef());
      // let apiId = this.field.options.api;
      // if (apiId) {
      //   // 获取参数配置
      //   let param = this.getRequestParam();
      //   // 分页设置
      //   let baseParam = {
      //     pageNum: type == 'all' ? 1 : this.currentPage,
      //     pageSize: type == 'all' ? 99999 : this.size,
      //   };
      //   let cols = this.field.options.cols;

      //   let excel2PdfParam = {
      //     body: { ...param, ...baseParam },
      //     apiId: apiId,
      //     cols: cols,
      //   };
      //   this.$ltPrintDialog.table2Pdf(excel2PdfParam);
      // } else {
      //   this.$print.print(this.getTableRef());
      // }
    },
    doExport(type) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }

      if (this.field.options.onClickExport) {
        let changeFn = new Function(this.field.options.onClickExport);
        changeFn.call(this);
        return;
      }

      let apiId = this.field.options.api;
      if (apiId) {
        // 获取参数配置
        let param = this.getRequestParam();
        // 分页设置
        let baseParam = {
          pageNum: type == "all" ? 1 : this.currentPage,
          pageSize: type == "all" ? 99999 : this.size,
        };
        let cols = this.field.options.cols;

        let excel2PdfParam = {
          body: { ...param, ...baseParam },
          apiId: apiId,
          cols: cols,
          isOutExcel: true,
        };
        excel2PdfByTable(excel2PdfParam).then((res) => {
          const fileName = type == "all" ? "全部数据.xlsx" : "当页数据.xlsx";
          const blob = new Blob([res], {
            type: `'application/vnd.ms-excel';charset=utf-8`,
          });
          const downloadElement = document.createElement("a");
          const href = window.URL.createObjectURL(blob);
          downloadElement.href = href;
          downloadElement.download = fileName;
          document.body.appendChild(downloadElement);
          downloadElement.click();
          document.body.removeChild(downloadElement);
          window.URL.revokeObjectURL(href);
        });
      } else {
        // eslint-disable-next-line no-undef
        $(`div[cust-sort-table-id=${this.$refs.sortTableRef.id}]`).table2excel({
          exclude: ".noExl",
          filename: this.field.options.label,
        });
      }
    },
    tableExport() {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.copyData.length > 0) {
        return this.copyData;
      }
    },
    getHeader() {
      let obj = {};
      this.copyCol.forEach((item) => {
        if (item.label && item.prop) {
          if (
            this.copyData.some(
              (e) =>
                typeof e[item.prop] == Number ||
                !Number.isNaN(Number(e[item.prop]))
            )
          ) {
            obj[item.label] = {
              field: item.prop,
              callback: (value) => {
                return "&nbsp;" + value;
              },
            };
          } else {
            obj[item.label] = item.prop;
          }
        }
      });

      return obj;
    },
    setLoading(flag) {
      this.loading = flag;
    },
    click(row, index, fun) {
      if (fun) {
        let JS = new Function("row", "index", fun);
        JS.call(this, row, index);
      }
    },
    handleLeftClick(fun) {
      if (fun) {
        let JS = new Function(fun);
        JS.call(this);
      }
    },
    handleLeftDisplay(displayFun) {
      try {
        if (!displayFun) {
          return true;
        } else {
          let cell = new Function(displayFun);
          return cell.call(this);
        }
      } catch (e) {
        return true;
      }
    },
    sortChange({ column, prop, order }) {
      this.sort.prop = prop;
      this.sort.order = order;
      this.loadData();
    },
    loadData(flag = true) {
      try {
        if (this.field.options.api) {
          // 获取参数配置
          let param = this.getRequestParam();

          let baseParam = {
            pageNum: this.currentPage,
            pageSize: this.size,
            sort: this.sort,
          };
          this.loading = true;

          executeInterface({
            apiId: this.field.options.api,
            body: { ...baseParam, ...param },
          })
            .then((res) => {
              if (res.data) {
                let temp =
                  ("records" in res.data ? res.data.records : res.data) || [];
                if (Array.isArray(temp)) {
                  this.updateTableData(temp);
                }
                if (flag) {
                  this.setRowSelected();
                  this.clearCurrentSelectData();
                }
                this.total = res.data?.total || 0;
                if (res.data && res.data.tableHeader != null) {
                  this.updateTableRow(res.data.tableHeader);
                }
                if (res.data.data) {
                  this.updateTableData(res.data.data);
                  if (res.data && res.data.tableHeader != null) {
                    this.updateTableRow(res.data.tableHeader);
                  }
                }
                //编辑状态下自动填充列
                if (
                  this.copyData &&
                  this.designState &&
                  (this.field.options.cols == null ||
                    this.field.options.cols.length == 0) &&
                  (this.copyCol == null || this.copyCol == 0)
                ) {
                  let keys = Object.keys(this.copyData[0]);

                  let cols = [];
                  let index = 1;
                  // TODO 修改数据表格列配置时候，这边也同步修改
                  keys.forEach((key) => {
                    if (key) {
                      let icon = "";
                      if (key.includes("time")) {
                        icon = "time";
                      } else if (key.includes("date")) {
                        icon = "date";
                      }
                      cols.push({
                        id: +new Date() + index,
                        label: key,
                        prop: key,
                        fix: "",
                        align: "",
                        width: "",
                        icon: icon,
                        code: ``,
                        sortable: false,
                        overflow: false,
                        link: false,
                        tag: false,
                        editable: true,
                        editType: "input",
                        total: 0,
                        isEdit: false,
                        other: "",
                        selectConfig: {
                          api: "",
                          onSuccessCallback: "",
                          onFailCallback: "",
                          selectLabel: "label",
                          selectValue: "value",
                          options: {
                            optionItems: [
                              {
                                label: "select 1",
                                value: "1",
                              },
                              {
                                label: "select 2",
                                value: "2",
                              },
                              {
                                label: "select 3",
                                value: "3",
                              },
                            ],
                          },
                          type: "select",
                          allowCreate: false,
                          // multiple: false,
                        },
                      });
                      index += 1;
                    }
                  });
                  this.field.options.cols = cols;
                }
                // 字典
                if ("dictMap" in res.data) {
                  this.dictMap = res.data.dictMap;
                }
                // $nextTick 不使用这个选中不了
                this.$nextTick(() => {
                  this.getTableRef().clearSelection()
                  // 消除未完成的选中列
                  this.getTableRef().doLayout();
                });
              }

              this.loading = false;
              setTimeout(() => {
                this.$nextTick(() => {
                  this.custKey = Math.random();
                  this.getTableRef().doLayout();
                });
              }, 200);

              setTimeout(() => {
                this.$nextTick(() => {
                  if (this.field.options.onSuccessCallback) {
                    let customFunc = new Function(
                      "res",
                      this.field.options.onSuccessCallback
                    );
                    customFunc.call(this, res);
                  }
                });
              }, 220);
            })
            .catch((err) => {
              this.loading = false;
              // this.$message.error(err.message);
              if (this.field.options.onFailCallback) {
                let customFunc = new Function(
                  "e",
                  this.field.options.onFailCallback
                );
                customFunc.call(this, err);
              }
            });
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    resizeHeight() {
      let hasHidden = $(
        "#data-table-" + this.stringToIntHash(this.field.id)
      ).is(":visible");
      if (!hasHidden) {
        setTimeout(() => {
          this.resizeHeight();
        }, 200);
      } else {
        let h =
          $("#data-table-" + this.stringToIntHash(this.field.id)).offset().top +
          10;
        //分页器高度
        if (this.total > 0) {
          h += 50;
        }
        this.computedHeight = `calc(100vh - ${h + 20}px)`;
      }
    },
    /**
     * 刷新表格数据
     * */
    updateRefresh(page = 1) {
      if (page) this.currentPage = page;
      this.loadData();
    },
    updateTableRow(cols) {
      // 过滤掉空对象
      cols = cols.filter((item) => Object.keys(item).length);
      this.copyCol = cols;
    },
    updateTableData(data) {
      let arr = JSON.parse(JSON.stringify(data));
      this.loading = true;
      this.copyData.splice(0, this.copyData.length);
      this.copyData.push(...arr);
      this.custKey = Math.random();
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    silenceUpdateTableData(data) {
      let arr = JSON.parse(JSON.stringify(data));
      this.copyData.splice(0, this.copyData.length);
      this.copyData.push(...arr);
      this.custKey = Math.random();
      this.$forceUpdate();
    },
    getCurrentSelectData() {
      return this.currentSelectData;
    },
    setChildren(children, type) {
      // 编辑多个子层级
      children.map((j) => {
        this.toggleSelection(j, type);
        if (j.children) {
          this.setChildren(j.children, type);
        }
      });
    },
    // 选中父节点时，子节点一起选中取消
    select(selection, row) {
      if (!this.field.options.linkAble) {
        return;
      }
      const hasSelect = selection.some((el) => {
        return (
          row[this.field.options.rowKey || "id"] ===
          el[this.field.options.rowKey || "id"]
        );
      });
      if (hasSelect) {
        if (row.children) {
          // 解决子组件没有被勾选到
          this.setChildren(row.children, true);
        }
      } else {
        if (row.children) {
          this.setChildren(row.children, false);
        }
      }
    },
    toggleSelection(row, select) {
      if (row) {
        this.$nextTick(() => {
          this.getTableRef() &&
            this.getTableRef().toggleRowSelection(row, select);
        });
      }
    },
    // 选择全部
    selectAll(selection) {
      if (!this.field.options.linkAble) {
        return;
      }
      // tabledata第一层只要有在selection里面就是全选
      const isSelect = selection.some((el) => {
        const tableDataIds = this.copyData.map(
          (j) => j[this.field.options.rowKey || "id"]
        );
        return tableDataIds.includes(el[this.field.options.rowKey || "id"]);
      });
      // tableDate第一层只要有不在selection里面就是全不选
      const isCancel = !this.copyData.every((el) => {
        const selectIds = selection.map(
          (j) => j[this.field.options.rowKey || "id"]
        );
        return selectIds.includes(el[this.field.options.rowKey || "id"]);
      });
      if (isSelect) {
        selection.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, true);
          }
        });
      }
      if (isCancel) {
        this.copyData.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, false);
          }
        });
      }
    },
    // 设置表格选中的值
    setCurrentSelectData(rows) {
      const { rowKey } = this.field.options;
      if (!rowKey) {
        this.$message({ message: "请设置数据表格的主键" });
        return;
      }

      if (!this.copyData || this.copyData.length == 0) {
        this.setCurrentSelectDataUnfinished.push(...rows);
      } else {
        this.copyData.forEach((item) => {
          if (rows.includes(item[rowKey])) {
            this.getTableRef().toggleRowSelection(item, true);
          } else {
            this.getTableRef().toggleRowSelection(item, false);
          }
        });
      }
    },
    // 消除设置初始化的时候未完成的选中项
    eliminateUnfinishedCheck() {
      if (
        this.setCurrentSelectDataUnfinished &&
        this.setCurrentSelectDataUnfinished.length > 0
      ) {
        const { rowKey } = this.field.options;
        this.copyData.forEach((item) => {
          if (setCurrentSelectDataUnfinished.includes(item[rowKey])) {
            this.getTableRef().toggleRowSelection(item, true);
          } else {
            this.getTableRef().toggleRowSelection(item, false);
          }
        });
      }
    },
    clearCurrentSelectData() {
      this.currentSelectData = [];
      this.getTableRef().clearSelection();
    },
    // 因为套了层sort-table,所以需要这样调用才能拿到 el-table表格 的ref
    getTableRef() {
      return this.$refs.sortTableRef && this.$refs.sortTableRef.$refs.tableRef;
    },
    getPageNum() {
      return this.currentPage;
    },
    getPageSize() {
      return this.size;
    },
    stringToIntHash(str, upperbound, lowerbound) {
      let result = 0;
      for (let i = 0; i < str.length; i++) {
        result = result + str.charCodeAt(i);
      }

      if (!lowerbound) lowerbound = 0;
      if (!upperbound) upperbound = 500;

      return (result % (upperbound - lowerbound)) + lowerbound;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      const { showIndex, hasSelection, subsetField } = this.field.options;
      let colData = [];
      this.$array.handleDFS(this.copyCol, "children", (item) => {
        if (!item.hidden && !item.children) {
          colData.push(item);
        }
      });

      // data转成扁平化的数据,以兼容树表格
      // let tableData = this.$array.treeToArray(data, subsetField);
      columns.forEach((column, index) => {
        if (column.label === "序号" && showIndex) {
          sums[index] = "";
        } else if (column.type === "selection" && hasSelection) {
          sums[index] = "";
        } else {
          const values = data.map((item) => Number(item[column.property]));
          // 考虑序号和多选的导致,索引进行后移
          let realIndex = index;
          if (hasSelection) --realIndex;
          if (showIndex) --realIndex;
          const { total } = colData[realIndex];
          if (!values.every((value) => isNaN(value)) && total) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] = sums[index].toFixed(2);
          } else {
            sums[index] = "";
          }
        }
      });

      return sums;
    },
    scrollChange(e) {
      this.getTableRef().bodyWrapper.scrollLeft = e;
    },
    handleExpandChange(row, expandedRows) {
      // element ui 累加z-index
      // 弹窗
      this.zCIndex = _popupManager.default.nextZIndex();
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onExpandChange) {
        let JS = new Function(
          "row",
          "expandedRows",
          this.field.options.onExpandChange
        );
        JS.call(this, row, expandedRows);
      }
    },
    handleHeaderDragend(newWidth, oldWidth, column, event) {
      if (
        this.field.options.btns.length > 0 &&
        this.copyCol.findIndex((item) => item.fix) !== -1
      ) {
        this.copyCol.forEach((item) => {
          if (item.prop === column.property && item.label === column.label) {
            item.width = newWidth;
          }
        });
      }
    },
    colChange() {
      let formDesign = this.$route.path;
      if (formDesign.indexOf("/formDesign") === -1) {
        let index = formDesign.lastIndexOf("/");
        let templateId = formDesign.slice(index + 1);
        let data = {
          templateId: templateId,
          tableId: this.field.id,
          cols: this.copyCol,
        };
        colsUpdate(data);
      }
    },
    /**
     * 主动触发editChange事件
     * @param {*} val
     * @param {*} fieldName
     * @param {*} rowIndex
     * @param {*} colIndex
     * @param {*} rowData
     * @param {*} colData
     * @param {*} extra
     */
    triggerEditChange(
      val,
      fieldName,
      rowIndex,
      colIndex,
      rowData,
      colData,
      extra
    ) {
      if (this.field.options.onEditChange) {
        let JS = new Function(
          "val",
          "fieldName",
          "rowIndex",
          "colIndex",
          "rowData",
          "colData",
          "extra",
          this.field.options.onEditChange
        );
        JS.call(
          this,
          val,
          fieldName,
          rowIndex,
          colIndex,
          rowData,
          colData,
          extra
        );
      }
    },
    /**
     * 设置编辑列不可编辑关系
     * @param {*} id
     * @param {*} relation
     */
    setDisableRelation(id, relation) {
      this.$set(this.disableRelation, id, relation);
    },
    /**
     * 数字处理
     * @param {*} val    值
     * @param {*} digit  保留几位小数
     * @param {*} round  是否四舍五入
     */
    handleNumber(val, digit, round) {
      if (/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(val)) {
        let num = val;
        num = num.toString();
        // 小数后截取
        let index = num.indexOf(".");
        if (index !== -1) {
          num = num.substring(0, digit + index + 1);
        } else {
          num = num.substring(0);
        }

        // 去掉最后一个.
        if (num.at(-1) === ".") {
          num = num.substring(0, num.length - 1);
        }
        // 四舍五入
        if (digit && round) {
          num = Math.round(num * Math.pow(10, digit)) / Math.pow(10, digit);
        }
        return num;
      }
      return val;
    },
  },
};
</script>
<style>
@page {
  size: auto;
  margin: 0mm;
}
</style>
<style lang="scss" scoped>
::v-deep .hide-checkbox {
  th .el-checkbox {
    display: none;
  }
}
::v-deep .el-dropdown-menu__item {
  color: #3177ff;
  text-align: center;
}

::v-deep .el-table__empty-block {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.ty {
  transform: translateY(1.5px);
}

.titleBox {
  position: relative;
  height: 36px;

  .leftBox {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .rightBox {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.el-row.grid-container {
  min-height: 50px;
  //line-height: 48px;
  //padding: 6px;
  outline: 1px dashed #336699;

  .form-widget-list {
    min-height: 28px;
  }
}



::v-deep .fixed-pre-first-field {
  z-index: 4;
}

::v-deep .el-table--scrollable-x {
  .fixed-pre-first-field {
    z-index: 0;
  }
}
::v-deep .el-table__fixed-right-patch{
  background: transparent;
}
.grid-container.selected,
.grid-cell.selected {
  outline: 2px solid #409eff !important;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  height: 16px;
}

::v-deep .leftBox .el-link {
  background: linear-gradient(0deg, #0096ff, #043475);
  border: 1px solid #548ddb;
  border-radius: 4px;
  color: #fff;
  padding: 0 10px;
  margin-right: 10px;
}

::v-deep .el-table__expanded-cell {
  z-index: 1900 !important;
}
</style>
