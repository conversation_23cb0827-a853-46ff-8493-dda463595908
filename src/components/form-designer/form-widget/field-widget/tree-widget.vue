<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <Treeselect
      v-model="fieldModel"
      class="full-width-input"
      :options="options"
      :disabled="field.options.disabled"
      :clearable="field.options.clearable"
      :searchable="field.options.filterable"
      :multiple="field.options.multiple"
      :normalizer="normalizer"
      :placeholder="
        field.options.placeholder || i18nt('render.hint.selectPlaceholder')
      "
      :disable-branch-nodes="disableBranchApiIds.includes(field.options.api)"
      @select="changeValue"
      @deselect="deslelct"
    />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { executeInterface } from '@/api/interfaces/interfaces';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { deepClone } from '@/utils/util';

// 需要disable-branch-nodes参数的接口
const disableBranchApiIds = [
  '7daad577d565457f84fe17ebb3291587', // 人员树
  'c4fe24b1ec3840a78ccbc13c91928ae3', // 设备树
];
// :api-id="field.options.api"
export default {
  name: 'TreeWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
    Treeselect,
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      value: null,
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],
      options: [],
      selectItem: null,
      disableBranchApiIds,
    };
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable && !!this.field.options.allowCreate
      );
    },
    getOptionsSelectValue() {
      return this.field.options.selectValue || 'id';
    },
    getSelectLabel() {
      return this.field.options.selectLabel || 'label';
    },
  },
  watch: {
    fieldModel() {
      if (this.fieldModel === '') {
        this.fieldModel = undefined;
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();


    if (this.field.options.multiple && typeof this.fieldModel == 'string') {
      this.fieldModel = this.fieldModel.split(',');
    }
  },
  mounted() {
    this.handleOnMounted();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    sendApi(){
      if (this.field.options.api) {
      executeInterface({
        apiId: this.field.options.api,
      }).then((res) => {
        this.options = res.data;
       });
     }
    },
    changeValue(data) {
      setTimeout(() => {
        if (this.field.options.multiple) {
          this.handleChangeEvent(this.fieldModel);
        } else {
          this.oldFieldValue = deepClone(this.fieldModel);
          this.fieldModel = data[this.getOptionsSelectValue];
          this.handleChangeEvent(data[this.getOptionsSelectValue]);
          this.selectItem = data;
        }
      }, 500);
    },
    deslelct(data) {
      this.handleChangeEvent(this.fieldModel);
      this.selectItem = data;
    },
    getSelectItem() {
      return this.selectItem;
    },
    normalizer(node) {
      //当子节点也就是children=[]时候去掉子节点
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node[this.getOptionsSelectValue],
        label: node[this.getSelectLabel],
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>


.full-width-input {
  width: 100% !important;
}

::v-deep .vue-treeselect__menu-container {
  font-size: 14px !important;
}
::v-deep .vue-treeselect__input{
  color:#fefefe;
}
::v-deep .vue-treeselect__menu{
  background:#104895 ;
  border-color: #025fb2;
  .vue-treeselect__option{
    background: transparent !important;
  }
}
::v-deep .vue-treeselect__value-container {
  font-size: 14px !important;
}
</style>
