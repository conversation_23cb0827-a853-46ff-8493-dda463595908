<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <VFormRender
      v-if="!loading && templateJson"
      ref="vFormRefLast"
      :form-json="templateJson"
      :form-data="initData"
      :writeState="false"
      :key="randomKey"
    />
    <div v-else>
      <el-skeleton :rows="6" animated />
    </div>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { getByTemplateIdInfo, getInfo } from '@/api/tool/form';
export default {
  name: 'vfrom-quote-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    itemObj: {
      type: Object,
      default: () => ({
        name: '111',
      }),
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      test: null,
      templateJson: null,
      formId: null,
      templateId: null,
      initData: {},
      keyToMap: {},
      aliasToMap: {},
      randomKey: '',
      loading: false
    };
  },
  computed: {
    inputType() {
      return this.field.options.type;
    },
    color() {
      if (!!this.field.options.color) {
        return this.field.options.color;
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.initAsData();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    initView() {
      this.loading = true
      this.templateJson = null
      if (this.templateId) {
        getByTemplateIdInfo(this.templateId)
          .then((res) => {
            let templateJson = {};
            if (res.data.templateJson) {
              templateJson = JSON.parse(res.data.templateJson);
            }
            func(templateJson);
            this.$nextTick(() => {
              this.randomKey = Math.random();
            });
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
          });
      } else {
        getInfo(this.formId, {})
          .then((res) => {
            let templateJson = {};
            if (res.data.templateJson) {
              templateJson = JSON.parse(res.data.templateJson);
            }
            func(templateJson);
            this.$nextTick(() => {
              this.randomKey = Math.random();
            });
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
          });
      }
      var func = (templateJson) => {
        // 迭代出别名和elementkey
        let cr = (arr) => {
          arr.forEach((item) => {
            // 如果有别名 设置别名
            if (item.options.alias) {
              this.keyToMap[item.options.alias] = item.id;
              this.aliasToMap[item.id] = item.options.alias;
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
             if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if(item.type=='table'&&item.rows&&item.rows.length>0){
              item.rows.forEach(e=>{
                cr(e.cols);
              })
            }
          });
        };
        //迭代
        cr(templateJson.widgetList);

        Object.getOwnPropertyNames(this.initData)
          .filter((item) => item != '__ob__')
          .forEach((name) => {
            if (
              name.includes('subform') ||
              (this.keyToMap[name] && this.keyToMap[name].includes('subform'))
            ) {
              this.initData[name].forEach((it) => {
                for (let k in it) {
                  if (this.keyToMap[k]) {
                    it[this.keyToMap[k]] = it[k];
                    delete it[k];
                  }
                }
              });
            }
            if (this.keyToMap[name]) {
              this.initData[this.keyToMap[name]] = this.initData[name];
              delete this.initData[name];
            }
          });
        this.templateJson = templateJson;
      };
    },

    setFormId(formId, initData) {
      this.formId = formId;
      this.initData = initData;
      this.initView();
    },
    setTemplateId(templateId, initData) {
      this.templateId = templateId;
      this.initData = initData;
      this.initView();
    },
    async validate(flag = true) {
      let dataForm = null;
      dataForm = flag
        ? await this.$refs.vFormRefLast.getFormData(true)
        : this.$refs.vFormRefLast.getFormData(false);
      Object.getOwnPropertyNames(dataForm).forEach((name) => {
        if (name.includes('subform')) {
          dataForm[name].forEach((it) => {
            for (let k in it) {
              if (this.aliasToMap[k]) {
                it[this.aliasToMap[k]] = it[k];
                delete it[k];
              }
            }
          });
        }
        if (this.aliasToMap[name] && name != '__ob__') {
          dataForm[this.aliasToMap[name]] = dataForm[name];
          delete dataForm[name];
        }
      });
      return dataForm;
    },
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}

</style>
