<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
  >
    <el-dropdown
      @click="handleClick"
      @command="handleCommand"
      :split-button="field.options.isButton"
      :placement="field.options.placement"
      :type="field.options.dropType"
      :size="field.options.dropSize"
      :trigger="field.options.trigger"
      :disabled="field.options.disabled"
      :hide-on-click="field.options.hideOnClick"
    >
      <span class="el-dropdown-link"> {{ field.options.label }} <i v-if="!field.options.isButton" class="el-icon-arrow-down el-icon--right"></i></span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          :command="item.command"
          :icon="item.icon"
          :disabled="item.disabled"
          :divided="item.divided"
          v-for="item in field.options.dropItems"
          :key="item.id"
          >{{ item.label }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
export default {
  name: "dropdown-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: 0,
      rules: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    this.handleOnMounted();
    if(this.field.options.dropItems.length==0&&this.field.options.autoLoadData){
      this.sendApi();
    }
  },
  methods: {
    sendApi(){
      if(this.field.options.api){
        let param = this.getRequestParam();
        executeInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.field.options.dropItems = res.data;
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    handleClick() {
      let JS = new Function(this.field.options.onClick);
      JS.call(this);
    },
    handleCommand(command) {
      let JS = new Function("command", this.field.options.onCommand);
      JS.call(this, command);
    },
  },
};
</script>

<style scoped>

.el-dropdown > .el-dropdown-link {
    cursor: pointer;
    color: #3177ff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
</style>
