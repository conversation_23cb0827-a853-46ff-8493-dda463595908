<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <template v-if="fileList.length > 0">
      <template v-for="(item, index) in fileList.slice(0,field.options.maxShow)">
        <el-image
        v-if="!field.options.showIndex ||  (index==field.options.showIndex)"
        :key="item.id"
        :style="{
          width: (field.options.width || 100) + 'px',
          height: (field.options.height || 100) + 'px',
          cursor: 'pointer',
          margin: '5px',
          display:
            field.options.direction == 'horizontal' ? 'inline-block' : 'block',
        }"
        :src="item.pathName"
        :fit="field.options.fit"
        @click.native="handleClick(item, index)"
      >
      </el-image>
      </template>
      
    </template>
    <lt-empty v-else></lt-empty>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import { getFile } from "@/api/file/file.js";

export default {
  name: "image-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      fileList: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    async setValue(ids) {
      this.fieldModel=ids
      let arr = Array.isArray(ids) ? ids : [ids];
      let url = this.$store.state.app.uploadUrl;
      this.fileList=[]
      for (let i = 0; i < arr.length; i++) {
        let res = await getFile({ fileId: arr[i] });
        res.data.forEach((item) => {
          item.pathName = url + item.pathName;
        });
        this.fileList.push(...res.data);
      }
    },
    handleClick(item,index) {
      if (this.field.options.onImageClick) {
        let customFunc = new Function("item",'index', this.field.options.onImageClick);
        customFunc.call(this, item, index);
      } else {
        this.$ltImgsPreview.open(
          this.fileList.map((item) => item.pathName),
          index
        );
      }
    },
  },
};
</script>

<style scoped>
</style>
