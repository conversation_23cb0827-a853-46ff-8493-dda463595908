<template>
  <span>
    <!-- 根据type区分是文字还是图标 -->
    <template v-if="btn.type === 2">
      <i
        v-if="btn.name && btn.name.includes('el-icon')"
        class="mr-r10 ty pointer"
        :style="{ color: btn.color }"
        :class="btn.name"
      />
      <svg-icon
        v-else
        :icon-class="btn.name"
        class="mr-r10 pointer"
        :style="{ color: btn.color }"
      />
    </template>
    <el-link
      v-else
      :underline="false"
      class="mr-r10"
      type="primary"
      :style="{ color: btn.color }"
      >{{ btn.name }}
    </el-link>
    <!--
      @click.native.stop="click(row, $index, btn.fun)"
      @click.stop="click(row, $index, btn.fun)"
    -->
  </span>
</template>

<script>
export default {
  name: 'TableCells',
  props: {
    btn: Object,
  },
};
</script>
