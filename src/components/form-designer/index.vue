<template>
  <el-container class="full-height">
    <el-container style="position:relative;overflow:hidden">
      <transition name="leftToggle">
        <el-aside class="side-panel" v-show="leftShow">
          <widget-panel :designer="designer" ref="widgetRef" />
        </el-aside>
      </transition>
      <div class="toggle-bar-left" :style="{ left: leftShow ? '255px' : '0' }" @click="leftShow = !leftShow">
        <i :class="[leftShow ? 'el-icon-caret-left' : 'el-icon-caret-right']"></i>
      </div>
      <el-container class="center-layout-container">
        <el-header class="toolbar-header">
          <toolbar-panel :designer="designer" :templateId="templateId" ref="toolbal"></toolbar-panel>
        </el-header>
        <el-main class="form-widget-main">
          <!-- <el-scrollbar class="container-scroll-bar" :style="{height: scrollerHeight}"> -->
          <v-form-widget :designer="designer" :form-config="designer.formConfig">
          </v-form-widget>
          <!-- </el-scrollbar> -->
        </el-main>
      </el-container>
      <div class="toggle-bar-right" :style="{ right: rightShow ? '280px' : '0' }" @click="rightShow = !rightShow">
        <i :class="[rightShow ? 'el-icon-caret-right' : 'el-icon-caret-left']"></i>
      </div>
      <transition name="rightToggle">
        <el-aside style="overflow:hidden;width:280px" v-show="rightShow">
          <setting-panel :designer="designer" :selected-widget="designer.selectedWidget"
            :form-config="designer.formConfig" />
        </el-aside>
      </transition>
    </el-container>
    <vfDialog ref="linkDialog" />
  </el-container>
</template>

<script>
import WidgetPanel from './widget-panel/index'
import ToolbarPanel from './toolbar-panel/index'
import SettingPanel from './setting-panel/index'
import VFormWidget from './form-widget/index'
import { createDesigner } from "@/components/form-designer/designer";
import { addWindowResizeHandler, deepClone, getQueryParam } from "@/utils/util";
import { MOCK_CASE_URL, VARIANT_FORM_VERSION } from "@/utils/config";
import i18n, { changeLocale } from "@/utils/i18n";
import bus from '@/scripts/bus'
import vfDialog from '@/views/tool/variantform/vfDialog.vue'
import { getInfo } from '@/api/tool/form'

export default {
  name: "VFormDesigner",
  componentName: "VFormDesigner",
  mixins: [i18n],
  components: {
    WidgetPanel,
    ToolbarPanel,
    SettingPanel,
    VFormWidget,
    vfDialog,
  },
  props: {
    fieldListApi: {
      type: Object,
      default: null,
    },
    templateId: "",
    inDesigner: {
      type: Object,
      default: {}
    }
  },

  data() {
    return {
      vFormVersion: VARIANT_FORM_VERSION,
      curLangName: '',

      vsCodeFlag: false,
      caseName: '',
      scrollerHeight: 0,
      designer: null,
      leftShow: true,
      rightShow: true,
      fieldList: []
    }
  },
  provide() {
    return {
      serverFieldList: this.fieldList,
    }
  },
  created() {
    this.vsCodeFlag = getQueryParam('vscode') == 1
    this.caseName = getQueryParam('case')
  },
  mounted() {
    this.initLocale()

    this.scrollerHeight = window.innerHeight - 56 - 36 + 'px'
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight = window.innerHeight - 56 - 36 + 'px'
      })
    })

    this.loadCase()

    this.loadFieldListFromServer()
    // 监听任务
    bus.$on('onOpenVfDialog', this.openVfDialog)

    if (this.designer.widgetList.length == 0) {
      this.$refs.widgetRef.open()
    }
  },
  methods: {
    openVfDialog(hasOpen, formId, data) {
      let dataKey = 'openVfDialog-' + formId
      localStorage.setItem(dataKey, JSON.stringify(data))
      getInfo(formId, {})
        .then((res) => {
          let formJson = ""
          if (res.data.templateJson) {
            formJson = JSON.parse(res.data.templateJson);
          } else {
            let designer = createDesigner(this);
            let widgetList = deepClone(designer.widgetList);
            let formConfig = deepClone(designer.formConfig);
            formJson = JSON.parse(
              JSON.stringify({ widgetList, formConfig }, null, '  '),
            );
          }
          this.$refs.linkDialog.open(hasOpen, formJson, dataKey)
        })
        .catch((err) => {
        });
    },
    openUrl(event, url) {
      if (!!this.vsCodeFlag) {
        const msgObj = {
          cmd: 'openUrl',
          data: {
            url
          }
        }
        window.parent.postMessage(msgObj, '*')
      } else {
        let aDom = event.currentTarget
        aDom.href = url
        //window.open(url, '_blank') //直接打开新窗口，会被浏览器拦截
      }
    },

    loadCase() {
      if (!this.caseName) {
        return
      }

      axios.get(MOCK_CASE_URL + this.caseName + '.txt').then(res => {
        if (!!res.data.code) {
          this.$message.error(this.i18nt('designer.hint.sampleLoadedFail'))
          return
        }

        this.setFormJson(res.data)
        this.$message.success(this.i18nt('designer.hint.sampleLoadedSuccess'))
      }).catch(error => {
        this.$message.error(this.i18nt('designer.hint.sampleLoadedFail') + ':' + error)
      })
    },

    initLocale() {
      let curLocale = localStorage.getItem('v_form_locale')
      if (!!this.vsCodeFlag) {
        curLocale = curLocale || 'en-US'
      } else {
        curLocale = curLocale || 'zh-CN'
      }
      this.curLangName = this.i18nt('application.' + curLocale)
      this.changeLanguage(curLocale)
    },

    loadFieldListFromServer() {
      if (!this.fieldListApi) {
        return
      }

      axios.get(this.fieldListApi.URL).then(res => {
        let labelKey = this.fieldListApi.labelKey || 'label'
        let nameKey = this.fieldListApi.nameKey || 'name'

        res.data.forEach(fieldItem => {
          this.fieldList.push({
            label: fieldItem[labelKey],
            name: fieldItem[nameKey]
          })
        })
      }).catch(error => {
        this.$message.error(error)
      })
    },

    handleLanguageChanged(command) {
      this.changeLanguage(command)
      this.curLangName = this.i18nt('application.' + command)
    },

    changeLanguage(langName) {
      changeLocale(langName)
    },

    setFormJson(formJson) {
      let modifiedFlag = false
      if (!!formJson) {
        if (typeof formJson === 'string') {
          modifiedFlag = this.designer.loadFormJson(JSON.parse(formJson))
        } else if (formJson.constructor === Object) {
          modifiedFlag = this.designer.loadFormJson(formJson)
        }

        if (modifiedFlag) {
          this.designer.emitHistoryChange()
        }
      }
    },

    getFormJson() {
      return {
        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig)
      }
    },

    //TODO: 增加更多方法！！

  },
  watch: {
    inDesigner: {
      immediate: true,
      handler(val) {
        if (val) {
          window.localStorage.setItem('widget__list__backup', JSON.stringify(val.widgetList))
          window.localStorage.setItem('form__config__backup', JSON.stringify(val.formConfig))
          this.designer = createDesigner(this)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/global.scss';

.rightToggle-enter-active {
  animation: rightTrans 0.3s;
  animation-timing-function: linear;
}

.rightToggle-leave-active {
  animation: rightTrans 0.3s reverse;
  animation-timing-function: linear;
}

.leftToggle-enter-active {
  animation: leftTrans .3s;
  animation-timing-function: linear;
}

.leftToggle-leave-active {
  animation: leftTrans .3s reverse;
  animation-timing-function: linear;
}

@keyframes rightTrans {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translate(0);
  }
}

@keyframes leftTrans {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translate(0);
  }
}

.el-container.full-height {
  height: 100%;
  overflow-y: hidden;
}

.toggle-bar-left {
  display: block;
  cursor: pointer;
  height: 52px;
  width: 10px;
  position: absolute;
  top: calc(50% - 6px);
  border-radius: 0 8px 8px 0;
  background: transparent;
  z-index: 8;
  padding-top: 16px;
  transition: left .3s;
  transition-timing-function: linear;

  &:active i,
  &:hover i {
    transform: rotate(180deg);
  }

  i {
    font-size: 16px;
    margin-left: -5px;
    color: #333;
  }
}

.toggle-bar-right {
  display: block;
  cursor: pointer;
  height: 52px;
  width: 10px;
  position: absolute;
  top: calc(50% - 6px);
  border-radius: 8px 0 0 8px;
  background: transparent;
  z-index: 8;
  padding-top: 16px;
  transition: right .3s;
  transition-timing-function: linear;

  &:hover i {
    transform: rotate(180deg);
  }

  i {
    font-size: 16px;
    color: #333;
  }
}

.el-container.center-layout-container {
  border-left: 2px dotted #EBEEF5;
  border-right: 2px dotted #EBEEF5;
}

.el-header.main-header {
  border-bottom: 2px dotted #EBEEF5;
  height: 48px !important;
  line-height: 48px !important;
  min-width: 800px;
}

div.main-title {
  font-size: 18px;
  color: #242424;
  display: flex;
  align-items: center;
  justify-items: center;

  img {
    cursor: pointer;
    width: 36px;
    height: 36px;
  }

  span.bold {
    font-size: 20px;
    font-weight: bold;
    margin: 0 6px 0 6px;
  }

  span.version-span {
    font-size: 14px;
    color: #101F1C;
    margin-left: 6px;
  }
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.el-dropdown-link {
  margin-right: 12px;
  cursor: pointer;
}

div.external-link a {
  font-size: 13px;
  text-decoration: none;
  margin-right: 10px;
  color: #606266;
}

.el-header.toolbar-header {
  font-size: 14px;
  border-bottom: 1px dotted #CCCCCC;
  height: 40px !important;
  //line-height: 42px !important;
}

.el-aside.side-panel {
  max-width: 255px !important;
  overflow-y: hidden;
}

.el-main.form-widget-main {
  padding: 0;

  position: relative;
  overflow-x: hidden;
}

.container-scroll-bar {

  ::v-deep .el-scrollbar__wrap,
  ::v-deep .el-scrollbar__view {
    overflow-x: hidden;
  }
}

::v-deep .el-form-item__content .el-input--medium .el-input-group__append {
  height: 34px !important;
}

::v-deep .el-select {
  width: 100%
}
</style>
