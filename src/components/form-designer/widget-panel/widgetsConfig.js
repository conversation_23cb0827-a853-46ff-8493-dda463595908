export const containers = [
  {
    type: 'grid',
    category: 'container',
    icon: 'grid',
    cols: [],
    options: {
      name: '',
      label: '',
      margin: {
        mt: '',
        mb: '',
        ml: '',
        mr: '',
      },
      padding: {
        pt: '',
        pb: '',
        pl: '',
        pr: '',
      },
      border2: {
        width: '',
        type: 'solid',
        color: '',
      },
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      gutter: 12,
      customClass: '', // 自定义css类名
    },
  },

  {
    type: 'table',
    category: 'container',
    icon: 'table',
    rows: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },

  {
    type: 'tab',
    category: 'container',
    icon: 'tab',
    // displayType: 'border-card',
    tabs: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      icon: '',
      // required: false,
      // validationHint: '',
      // tableShow: false,
      // tableOrder: false,
      tabType: 'border-card',
      tabPosition: 'top',
      stretch: false,
      tabChange: '',
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'sub-form',
    category: 'container',
    icon: 'sub-form',
    widgetList: [],
    options: {
      name: '',
      label: '',
      bandKey: '',
      alias: '',
      authority: false,
      samePage: true,
      groupPage: false,
      showBlankRow: true,
      showRowNumber: true,
      labelAlign: 'label-center-align',
      required: false,
      readonly: false,
      hasRole: true,
      addAble: false,
      deleteAble: false,
      rowEdit: false,
      validationHint: '',
      subFormCols: [],
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: [],
      onSubFormRowAdd: '',
      onSubFormRowInsert: '',
      onSubFormRowDelete: '',
      onSubFormRowChange: '',
      onSubSave: '',
    },
  },
  {
    type: 'grid-col',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      span: 12,
      offset: 0,
      push: 0,
      pull: 0,
      border2: {
        width: '',
        type: 'solid',
        color: '',
      },
      responsive: false, // 是否开启响应式布局
      md: 12,
      sm: 12,
      xs: 12,
      customClass: '', // 自定义css类名
      margin: {
        mt: '',
        mb: '',
        ml: '',
        mr: '',
      },
      padding: {
        pt: '',
        pb: '',
        pl: '',
        pr: '',
      },
    },
  },
  {
    type: 'table-cell',
    category: 'container',
    icon: 'table-cell',
    internal: true,
    widgetList: [],
    merged: false,
    options: {
      name: '',
      label: '',
      authority: false,
      cellWidth: '',
      cellHeight: '',
      colspan: 1,
      rowspan: 1,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },

  {
    type: 'tab-pane',
    category: 'container',
    icon: 'tab-pane',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      active: false,
      disabled: false,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'card',
    category: 'container',
    icon: 'card',
    widgetList: [],
    options: {
      name: '',
      label: '卡片',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      folded: false,
      showFold: true,
      cardWidth: '100%',
      shadow: 'never',
      subtitle: '',
      customClass: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container',
    category: 'container',
    icon: 'grid',
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      customClass: '',
      onSearchClick: '',
      onResetClick: '',
    },
  },
  {
    type: 'trends-tab',
    category: 'container',
    icon: 'tab',
    // displayType: 'border-card',
    widgetList: [],
    options: {
      name: '',
      label: '动态选项卡',
      tabType: 'border-card',

      authority: false,
      // required: false,
      // validationHint: '',
      hidden: false,
      readonly: false,
      icon: '',
      // tableShow: false,
      // tableOrder: false,
      // tableShowWeight: 0,
      tabPosition: 'top',
      stretch: false,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'dialog-body',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'dialog-footer',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container-footer',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container-body',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'collapse-item',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },
];

export const basicFields = [
  {
    type: 'input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      value: '',
      alias: '',
      labelAlign: 'label-center-align',
      type: 'text',
      authority: false,
      bandKey: '',
      prepend: '',
      append: '',
      indexes: '',
      defaultValue: '',
      placeholder: '',

      size: '',
      color: '',
      labelColor: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      showPassword: false,
      required: false,
      validation: '',
      validationHint: '',

      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      // showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      onPrependClick: '',
      onAppendClick: '',
    },
  },
  {
    type: 'popup-select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      placeholder: '',
      dialogTable: '',
      bandKey: '',
      authority: false,
      labelWidth: null,
      labelHidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      customClass: '', // 自定义css类名
      // labelTooltip: null,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      showPassword: false,
      required: false,
      popupSelect: '',
      popupSelectValue: '',
      optionItems: [{ label: 'select 1', value: '' }],
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      api: '',
      paramConfig: '',
      autoLoadData: true,
      bandDataIdsApi: '',
    },
  },

  {
    type: 'order-input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      labelAlign: 'label-center-align',
      type: 'text',
      bandKey: '',
      authority: false,
      defaultValue: '',
      placeholder: '',

      size: '',
      labelWidth: null,
      labelHidden: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      busType: '',
      showPassword: false,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },
  {
    type: 'textarea',
    icon: 'textarea-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      value: '',
      authority: false,
      // indexes: '',
      labelAlign: 'label-center-align',
      rows: 3,
      defaultValue: '',
      placeholder: '',
      showReply: false,
      defaultReply: ['同意', '驳回', '已核实'],
      custom: false,
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'number',
    icon: 'number-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      value: '',
      defaultValue: 0,
      placeholder: '',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      readonly: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      // -------------------
      customClass: '', // 自定义css类名
      min: -100000000000,
      max: 100000000000,
      precision: 0,
      step: 1,
      controlsPosition: 'right',
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'radio',
    icon: 'radio-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      optionItems: [
        { value: 1, label: 'new option' },
        { value: 2, label: 'new option' },
      ],
      required: false,
      validation: '',
      validationHint: '',
      api: '',
      paramConfig: '',
      autoLoadData: true,
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'checkbox',
    icon: 'checkbox-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      showCheckAll: false,
      labelAlign: 'label-center-align',
      defaultValue: [],
      api: '',
      paramConfig: '',
      autoLoadData: true,
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      optionItems: [
        { value: '1', label: 'new option' },
        { value: '2', label: 'new option' },
      ],
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      api: '',
      paramConfig: '',
      autoLoadData: true,
      labelAlign: 'label-center-align',
      color: '',
      labelColor: '',
      value: '',
      placeholder: '',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      filterable: false,
      collapseTag: false,
      allowCreate: false,
      remote: false,
      automaticDropdown: false, // 自动下拉
      multiple: false,
      multipleLimit: 0,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      selectValue: 'value',
      selectLabel: 'label',
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onRemoteQuery: '',
      onChange: '',
      allowCreateFn: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      // bandDataIdsApi: '',
    },
  },
  {
    type: 'tree',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      placeholder: '请选择数据',
      selectValue: 'id',
      selectLabel: 'label',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,

      clearable: true,
      filterable: true,
      // allowCreate: false,
      // remote: false,
      // automaticDropdown: false, // 自动下拉
      multiple: false,
      // multipleLimit: 0,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onRemoteQuery: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      api: '',
      autoLoadData: true,
      // bandDataIdsApi: '',
      // bandKey: '',
    },
  },
  {
    type: 'select-page',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: '',
      placeholder: '',
      size: '',
      labelWidth: null,
      dialogtableShow: false,
      tableOrder: false,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      clearable: true,
      multiple: false,
      isUser: false,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onRemoteQuery: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      api: '',
      autoLoadData: true,
      paramConfig: '',
      // bandDataIdsApi: '',
    },
  },
  {
    type: 'time',
    icon: 'time-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      placeholder: '',

      size: '',
      value: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'time-range',
    icon: 'time-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',

      size: '',
      value: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'date',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'date',
      defaultValue: null,
      defaultCurrent:false,
      placeholder: '',
      size: '',
      value: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      onDisabledDate: '',
    },
  },
  {
    type: 'date-time',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'datetime',
      defaultValue: '',
      placeholder: '',

      size: '',
      value: '',
      labelWidth: null,
      labelHidden: false,
      defaultTime: false,
      autoRefresh: false,
      // readonly: true,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },
  {
    type: 'date-range',
    icon: 'date-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'daterange',
      // defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',
      shortcuts: true,
      size: '',
      value: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      defaultRange: '',
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'switch',
    icon: 'switch-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: false,

      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      switchWidth: 40,
      activeText: '',
      inactiveText: '',
      activeColor: null,
      inactiveColor: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'rate',
    icon: 'rate-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,

      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      max: 5,
      lowThreshold: 2,
      highThreshold: 4,
      allowHalf: false,
      showText: false,
      showScore: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'color',
    icon: 'color-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,

      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'slider',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',

      showStops: true,
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: 0,
      max: 100,
      step: 10,
      range: false,
      // vertical: false,
      height: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'static-text',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      label: '',
      name: '',
      hidden: false,
      authority: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      textContent: 'static text',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'html-text',
    icon: 'html-text',
    formItemFlag: false,
    options: {
      name: '',

      hidden: false,
      tableShow: false,
      authority: false,
      tableOrder: false,
      tableShowWeight: 0,
      htmlContent: '<b>html text</b>',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'button',
    icon: 'button',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      alreadyClickLable: '',
      bandKey: '',
      authority: false,

      size: '',
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      type: '',
      plain: false,
      round: false,
      circle: false,
      icon: null,
      iconColor: undefined,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onClick: '',
    },
  },

  {
    type: 'divider',
    icon: 'divider',
    formItemFlag: false,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,

      direction: 'horizontal',
      contentPosition: 'center',
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'userinput',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '当前登录人',
      labelAlign: 'label-center-align',
      authority: false,
      alias: '',
      type: 'text',
      bandKey: '',
      // defaultValue: '',
      placeholder: '',
      required: false,
      size: '',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      labelWidth: null,
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      // bandDataIdsApi: '',

      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'deptinput',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '当前登录人',
      labelAlign: 'label-center-align',
      type: 'text',
      authority: false,
      bandKey: '',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // defaultValue: '',
      placeholder: '',

      size: '',
      labelWidth: null,
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      // bandDataIdsApi: '',

      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'borad',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      color: '#000',
      bgColor: '#fff',
      writeWidth: 5,
      required: false,
      customClass: '', // 自定义css类名
      onMounted: '',
      width: 500,
      height: 250,
    },
  },
];

export const advancedFields = [
  {
    type: 'picture-upload',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      // bandKey: '',
      authority: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      defaultValue: null,
      labelAlign: 'label-center-align',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      uploadTip: '',
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, // MB
      fileTypes: ['jpeg', 'png'],
      // headers: [],
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onBeforeUpload: '',
      onUploadSuccess: '',
      onUploadError: '',
      onValidate: '',
      // onFileChange: '',
    },
  },

  {
    type: 'file-upload',
    icon: 'file-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      authority: false,
      bandKey: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      readonly: false,
      hidden: false,
      fileUploadType: 'drag',
      fileUploadButtonLabel: '选取文件',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      uploadURL: '',
      // -------------------
      isShowTip: true,
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, // MB
      fileTypes: ['doc', 'docx', 'xls', 'xlsx'],

      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onBeforeUpload: '',
      onUploadSuccess: '',
      onUploadError: '',
      onValidate: '',
      // onFileChange: '',
    },
  },

  {
    type: 'rich-editor',
    icon: 'rich-editor-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      placeholder: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      labelAlign: 'label-center-align',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onValidate: '',
    },
  },

  {
    type: 'cascader',
    icon: 'cascader-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: '',
      placeholder: '',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      showAllLevels: true,
      clearable: true,
      filterable: false,
      collapseTag: false,
      api: '',
      multiple: false,
      checkStrictly: false,
      paramConfig: '',
      autoLoadData: true,
      selectValue: 'value',
      selectLabel: 'label',
      selectChildren: 'children',
      optionItems: [
        {
          label: 'select 1',
          value: 1,
          children: [{ label: 'child 1', value: 11 }],
        },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },
];

export const customFields = [];

export const viewFields = [
  {
    type: 'chart',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      theme: 'macarons',
      height: 300,
    },
  },
  {
    type: 'map',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      readonly: false,
      // -------------------
      customClass: '', // 自定义css类名
      width: 300,
      height: 300,
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'position',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      readonly: false,
      // -------------------
      customClass: '', // 自定义css类名
      hidden: true,
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'qrcode',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      width: 100,
      textarea: '',
      upload: '',
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'vfrom-quote',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      label: '',
      labelHidden: false,
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'custom-tree',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      placeholder: '',
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      onLoad: '', // 加载子树数据的方法，仅当 lazy 属性为true 时生效 function(node, resolve)
      onRenderContent: '', //树节点的内容区的渲染 Function	Function(h, { node, data, store }
      onDefaultExpandedKeys: '', // 默认展开的节点数组 Function(data) return array
      onDefaultCheckedKeys: '', // 默认勾选的节点的 key 的数组 默认展开的节点数组 Function(data) return array
      onFilterNodeMethod: '', // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏 Function(value, data, node)
      onAllowDrag: '', // 判断节点能否被拖拽  Function(node)
      onAllowDrop: '', // 拖拽时判定目标节点能否被放置。type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后 Function(draggingNode, dropNode, type)
      onNodeClick: '',
      onNodeCheck: '',
      indent: 16, //相邻级节点间的水平缩进，单位为像素
      iconClass: '', // 自定义树节点的图标
      isLazy: false,
      heightFit: true,
      api: '',
      autoLoadData: true,
      hasSearch: true,
      onLazy: '',
      expandAll: false,
      highlightCurrent: false, // 是否高亮当前选中节点，默认值是 false
      defaultExpandAll: false, // 是否默认展开所有节点
      expandOnClickNode: true, //是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
      checkOnClickNode: false, // 是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点
      autoExpandParent: true, // 展开子节点的时候是否自动展开父节点
      showCheckbox: false, // 节点是否可被选择
      checkStrictly: false, // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
      emptyText: '暂无数据', //内容为空的时候展示的文本
      nodeKey: '', // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
      draggable: false, //是否开启拖拽节点功能
      onScrollEnd: '',
      onNodeStyle: '',
    },
  },
  {
    type: 'carousel',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      height: '',
      interval: 3000,
      direction: 'horizontal',
      isCard: false,
      autoplay: true,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'timeline',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      activities: [],
      reverse: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'calendar',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      customHtml: false,
      slot: ``,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
    },
  },
  {
    type: 'barCode',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      barCode: '1',
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'progress', //进度条
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      LineWidth: 126,
      progressType: 'line',
      strokeWidth: 6, //默认 6
      progressTextInside: false,
      progressStatus: null,
      progressShowText: true,
      progressTextColor: '',
      progressDefineBackColor: '',
      progressColor: '',
      onCreated: '',
      onMounted: '',
      onProgressFormat: '',
    },
  },
  {
    type: 'transfer',
    icon: 'tab',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      filterable: false,
      placeholder: '请输入搜索内容',
      selectValue: 'value',
      selectLabel: 'label',
      api: '',
      paramConfig: '',
      autoLoadData: true,
      subtitle: '目标',
      targetOrder: 'push',
      title: '数据源',
      height: '',
      onChange: '',
      customClass: '', // 自定义css类名
      onMounted: '',
    },
  },
  {
    type: 'image',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      alias: '',
      label: '',
      width: 100,
      height: 100,
      fit: 'fill',
      maxShow: undefined,
      showIndex: undefined,
      direction: 'horizontal',
      customClass: '', // 自定义css类名
      onMounted: '',
      onImageClick: '',
    },
  },
  {
    type: 'codeConfig',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      codeManage: '',
      direction: 'horizontal',
      onMounted: '',
      codeId: null,
    },
  },
  {
    type: 'steps',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      steps: [],
      processStatus: 'process',
      finishStatus: 'process',
      direction: 'horizontal',
      alignCenter: false,
      customClass: '',
      simple: false,
      space: '',
      onMounted: '',
      onChange: '',
    },
  },
  {
    type: 'descriptions',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      title: '',
      rows: 3,
      hasFrame: true,
      widgetSize: 'medium',
      direction: 'horizontal',
      customClass: '',
      api: '',
      paramConfig: '',
      autoLoadData: true,
      selectLabel: 'label',
      selectValue: 'value',
      onMounted: '',
      onValueClick: '',
    },
  },
  {
    type: 'onlineComponents',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      onlineRelevance: '',
      // -------------------
      onCreated: '',
      onMounted: '',
      onHandleEmit: `/*
  接收在线组件通过dispatch传递出来的参数
*/
`,
    },
  },
  {
    type: 'webSocket',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: true,
      onCreated: '',
      onMounted: '',
      onMessage: '',
      rowKey: '',
    },
  },
  {
    type: 'tablePrint',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: true,
      reportRelevance: '',
      isShowExport: true,
      isShowPrint: true,
      isShowQuery: true,
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'dropdown',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      dropItems: [
        {
          id: 1,
          label: '选项',
          command: 'test',
          disabled: false,
          divided: false,
          icon: 'el-icon-setting',
        },
      ],
      isButton: false,
      placement: 'bottom-end',
      dropType: 'primary',
      dropSize: 'medium',
      disabled: false,
      hideOnClick: true,
      trigger: 'hover',
      api: '',
      paramConfig: '',
      autoLoadData: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onClick: '',
      onCommand: '',
    },
  },
];
export const viewIntegrateFields = [
  {
    type: 'flow-bus-list',
    icon: 'tab',
    formItemFlag: true,
    options: {
      name: '',
      flowType: '',
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'data-table',
    icon: 'table',
    formItemFlag: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelHidden: true,
      authority: false,
      bandKey: '',
      alias: '',
      // operation: false,
      data: [],
      hidden: false,
      height: '',
      isLazy: false,
      minHeight: '',
      api: '',
      paramConfig: '',
      disabled: false,
      readonly: false,
      hasFrame: true,
      hasPrint: true,
      hasExpand: false,
      hasExport: true,
      cols: [],
      btns: [],
      leftBtns: [],
      highlightCurrentRow: false,
      treeTable: false,
      subsetField: 'children',
      autoLoadData: true,
      hasScrollBar: true,
      showIndex: false,
      isShowTableField: true,
      tableStripe: false,
      showSummary: false,
      pageStyle: '',
      pagerCount: 7,
      rowKey: '',
      onCurrentChange: '',
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      heightFit: false,
      onClickPrint: '',
      onClickExport: '',
      onCellDblclick: '',
      onExpandChange: '',
      onEditChange: '',
      onBlurChange: '',
      onFocusChange: '',
      linkAble: false,
      defaultExpandAll: false,
      selectionLimit: undefined,
      hasSelection: 'false',
      onSelection: '',
      onRowStyle: '',
      onRowClass: '',
      onCellStyle: '',
      onSpanMethod: '',
      onCellClick: '',
      onRowClick: '',
      onSortChange: '',
      onLazy: '',
      isEnable: false,
      // numberMode: {
      //   open: false,
      //   digit: 0,
      //   round: false,
      // },
    },
  },
  {
    type: 'data',
    icon: 'table',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      hidden: true,
      api: '',
      paramConfig: '',
      onCreated: '',
      onMounted: '',
      onBeforeDestroy: '',
    },
  },
  {
    type: 'organizational-structure',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      placeholder: '请输入搜索内容',
      showNodeType: [],
      organizationalList: {},
      multiple: true,
      hidden: false,
      checkStrictly: true,
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      onNodeClick: '',
      onNodeCheck: '',
    },
  },
];
export const viewLayoutFields = [
  {
    type: 'iteration',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      onMounted: '',
      hidden: false,
      customClass: '',
    },
  },

  {
    type: 'condition',
    category: 'container',
    icon: 'grid',
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      customClass: '',
      onQuery: '',
    },
  },
  {
    type: 'custom-condition',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      alias: '',
      authority: false,
      api: '',
      paramConfig: '',
      autoLoadData: true,
      hidden: false,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      selectValue: 'value',
      selectLabel: 'label',
      onCreated: '',
      onMounted: '',
      // onChange: '',
      // onValidate: '',
      // bandDataIdsApi: '',
    },
  },
  {
    type: 'dialog',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      label: '弹出层',
      mark: '',
      hidden: true,
      dialogWidth: 60,
      closeOnClickModal: true,
      destroyOnClose: false,
      fullscreen: false,
      showClose: true,
      isCustom: false,
      leftBtns: [],
      hasCloseTips: false,
      isDrag: false,
      hasCloseTipsContent: '确认关闭？',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onDialogClose: '',
    },
  },
  {
    type: 'drawer',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      label: '',
      hidden: true,
      width: '',
      drawerDirection: 'ltr',
      customClass: '', // 自定义css类名
      onCreated: '',
    },
  },
  {
    type: 'collapse',
    category: 'container',
    icon: 'grid',
    items: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      accordion: false,
      collapse: '',
      customClass: '', // 自定义css类名
    },
  },
];

export function addContainerWidgetSchema(containerSchema) {
  containers.push(containerSchema);
}

export function addBasicFieldSchema(fieldSchema) {
  basicFields.push(fieldSchema);
}

export function addAdvancedFieldSchema(fieldSchema) {
  advancedFields.push(fieldSchema);
}

export function addCustomWidgetSchema(widgetSchema) {
  customFields.push(widgetSchema);
}
export function addViewFieldsWidgetSchema(widgetSchema) {
  viewFields.push(widgetSchema);
}
