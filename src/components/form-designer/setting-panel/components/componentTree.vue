<template>
  <div class="componentTree" @contextmenu.prevent>
     <div class="d-flex a-center">
        <el-input placeholder="搜索组件" clearable="" size="mini" v-model="filterText">
        </el-input>
        <i
          :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          @click="changeExpand"
          class="mr-l10"
          style="cursor: pointer; font-size: 16px"
          :title="isExpand ? '收缩' : '展开'"
        ></i>
      </div>

    <el-tree
      ref="nodeTree"
      :data="nodeTreeData"
      node-key="id"
      :default-expand-all="isExpand"
      highlight-current
      class="node-tree"
      icon-class="el-icon-arrow-right"
      :expand-on-click-node="false"
      @node-click="onNodeTreeClick"
      :filter-node-method="filterNode"
      :indent="10"
    >
      <template #default="{ node }">
        <div @contextmenu.prevent="showMenu($event, node)" style="width: 150px">
          {{ node.label }}
        </div>
      </template>
    </el-tree>
    <treeContextMenu ref="treeContextMenuRef" />
  </div>
</template>

<script>
import treeContextMenu from "./treeContextMenu.vue";
export default {
  name: "ComponentTree",
  components: {
    treeContextMenu,
  },
  data() {
    return {
      nodeTreeData: [],
      filterText: "",
      isExpand: true,
    };
  },
  watch: {
    filterText(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  methods: {
    changeExpand() {
      this.isExpand = !this.isExpand;
      for (
        var i = 0;
        i < this.$refs.nodeTree.store._getAllNodes().length;
        i++
      ) {
        // 根据isExpand， tree展开或折叠
        this.$refs.nodeTree.store._getAllNodes()[i].expanded = this.isExpand;
      }
    },

    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let level = node.level;
      let _array = []; //这里使用数组存储 只是为了存储值。
      this.getReturnNode(node, _array, value);
      let result = false;
      _array.forEach((item) => {
        result = result || item;
      });
      return result;
    },
     getReturnNode(node, _array, value) {
      let isPass =
        node.data && node.data.label && node.data.label.indexOf(value) !== -1;
      isPass ? _array.push(isPass) : "";
      if (!isPass && node.level != 1 && node.parent) {
        this.getReturnNode(node.parent, _array, value);
      }
    },
    init(widgetList) {
      this.nodeTreeData.length = 0;
      widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.type,
        type: widget.type,
        //selectable: true,
      };
      treeNode.push(curNode);

      if (widget.category === undefined) {
        return;
      }
      curNode.children = [];

      if (widget.type === "grid") {
        widget.cols.map((col) => {
          // let colNode = {
          //   id: col.id,
          //   label: col.options.name || widget.type,
          //   type: col.type,
          //   children: [],
          // };
          // curNode.children.push(colNode);
          col.widgetList &&
            col.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, curNode.children);
            });
        });
      } else if (widget.type === "table") {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: "table-row",
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: "table-cell",
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === "tab") {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            label: tab.options.name || widget.type,
            selectable: false,
            children: [],
            type: tab.type,
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === "sub-form") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "trends-tab") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if(widget.type === "collapse"){
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }else if (widget.category === "container") {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    async onNodeTreeClick(e) {
      try {
        await navigator.clipboard.writeText(e.id);
        this.$message({
          message: "复制成功:" + e.id,
          type: "success",
        });
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    },
    showMenu(e, node) {
      this.$refs.treeContextMenuRef.show({
        top: e.clientY,
        left: e.clientX,
        data: node,
      });
    },
  },
};
</script>

<style lang="scss">
.componentTree {
  width: 200px;
  height: 570px;
  margin-right: 10px;
  overflow: auto;
}
</style>
