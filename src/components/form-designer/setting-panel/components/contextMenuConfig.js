/**
 * title: 右键菜单的菜单名称
 * codeBlock: 触发的代码块, #id为占位符带指组件的唯一id
 * widgets: true->所有组件都有  ['input']->只有input才有
 */

const menuData = [
  {
    title: '获取组件的实例对象',
    codeBlock: 'this.getWidgetRef(#id)',
    widgets: true,
  },
  {
    title: '设置组件label值',
    codeBlock: `/**
 * 设置组件label值
 * @param { label } string
 */
this.getWidgetRef(#id).setLabel(label)
`,
    widgets: true,
  },
  {
    title: '获取组件的别名',
    codeBlock: 'this.getWidgetRef(#id).getAlias()',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'custom-condition',
    ],
  },

  {
    title: '获取组件的值',
    codeBlock: 'this.$getValue(#id)',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'custom-condition',
      'transfer',
      'descriptions',
      'rich-editor',
      'progress',
      'checkbox',
      'static-text',
      'pictureupload',
      'fileupload',
      'map',
      'number',
      'tree',
      'time',
      'date',
      'date-time',
      'switch',
      'rate',
      'color',
      'slider',
      'html-text',
      'codeConfig',
      'image',
      'timeline',
      'carousel',
      'popup-select',
      'qrcode',
      'barCode',
    ],
  },
  {
    title: '设置组件的值',
    codeBlock: 'this.$setValue(#id, )',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'custom-condition',
      'transfer',
      'descriptions',
      'rich-editor',
      'progress',
      'checkbox',
      'static-text',
      'pictureupload',
      'fileupload',
      'map',
      'number',
      'tree',
      'time',
      'date',
      'date-time',
      'switch',
      'rate',
      'color',
      'slider',
      'html-text',
      'codeConfig',
      'image',
      'timeline',
      'carousel',
      'popup-select',
      'qrcode',
      'barCode',
    ],
  },
  {
    title: '设置组件是否隐藏',
    codeBlock: `/**
 * 设置组件是否隐藏
 * @param { val } Boolean true:隐藏 false:显示
 */
this.getWidgetRef(#id).setHidden(val)
`,
    widgets: true,
  },
  {
    title: '设置组件是否必填',
    codeBlock: `/**
 * 设置组件是否必填
 * @param { val } Boolean true false
 */
this.getWidgetRef(#id).setRequired(val)
`,
    widgets: ['input', 'select', 'date-range', 'cascader', 'time-range'],
  },
  {
    title: '数据表格刷新数据',
    codeBlock: `/**
 * 数据表格刷新数据
 * @param { page } Number|Boolean 指定页码,默认为1,传入0或者false则为当前页刷新
 */
this.getWidgetRef(#id).updateRefresh(1)
`,
    widgets: ['data-table'],
  },
  {
    title: '获取表格列属性',
    codeBlock: 'this.getWidgetRef(#id).getCol()',
    widgets: ['data-table'],
  },
  {
    title: '设置表格列属性',
    codeBlock: `/**
    cols数据格式:[{
      label: "", //表头
      prop: "", //字段
      fix: "",  //固定
      align: "",  //文字对齐
      width: "",  //宽度
      editable: true,  //可编辑
      editType: "input",  //编辑方式
      icon: "",  //图标
      code: '',  //自定义html
      overflow: true,   //溢出显示
      link: false,   //链接
      tag: false,    //标签
      total: 0,    //合计
      hidden: false,   //隐藏
      isEdit: true,
    },...]
  this.getWidgetRef(#id).updateTableRow(cols)`,
    widgets: ['data-table'],
  },

  {
    title: '设置表格数据',
    codeBlock: 'this.getWidgetRef(#id).updateTableData(data)',
    widgets: ['data-table'],
  },
  {
    title: '获取表格当前选中的数据',
    codeBlock: 'this.getWidgetRef(#id).getCurrentSelectData()',
    widgets: ['data-table'],
  },
  {
    title: '设置加载动画状态',
    codeBlock: `/**
 * 设置加载动画状态
 * @param { val } Boolean true:开启 false:关闭
 */
this.getWidgetRef(#id).setLoading(val)
`,
    widgets: ['data-table', 'button', 'custom-tree'],
  },

  {
    title: '设置表格列标签字典',
    codeBlock:
      'this.getWidgetRef(#id).setDictMap({"列属性": {"类型一": "success", "类型二":"error"}})',
    widgets: ['data-table'],
  },
  {
    title: '清除表格当前选中数据',
    codeBlock: 'this.getWidgetRef(#id).clearCurrentSelectData()',
    widgets: ['data-table'],
  },

  {
    title: '设置组件是否只读',
    codeBlock: `/**
 * 设置组件是否只读
 * @param { val } Boolean true:只读 false:可写
 */
this.getWidgetRef(#id).setReadonly(val)
`,
    widgets: ['input', 'select', 'date-range', 'cascader', 'time-range'],
  },
  {
    title: '设置组件是否禁用',
    codeBlock: `/**
 * 设置组件是否禁用
 * @param { val } Boolean true:禁用 false:不禁用
 */
this.getWidgetRef(#id).setDisabled(val)
`,
    widgets: [
      'input',
      'select',
      'date-range',
      'cascader',
      'button',
      'time-range',
    ],
  },

  {
    title: '获取当前选择的对象',
    codeBlock: 'this.getWidgetRef(#id).getSelectItem()',
    widgets: ['select', 'select-page'],
  },
  {
    title: '设置选项',
    codeBlock: 'this.getWidgetRef(#id).reloadOptions(options)',
    widgets: ['select', 'select-page'],
  },
  {
    title: '设置选项(会清空已选择的内容)',
    codeBlock: 'this.getWidgetRef(#id).loadOptions(options)',
    widgets: ['select', 'select-page', 'cascader'],
  },
  {
    title: '报表打印',
    codeBlock: `/*** 报表打印
 * @param { queryParams }   参数说明见下
 *    common?: Object,            // 公共参数
 *    pageNu
 m?: Number,           // 页码
 *    pageSize?: Number,          // 每页的条数
 *    axios?: Object,             // axios请求对象
 *    printConfig?: {
 *      copies?: number;          // 份数
 *      paperSizeType?: number;   // 纸张大小, 见 papersizeTypeEnum
 *      printLayoutType?: number; // 布局类型 0:默认 1:平居中 2:垂直居中 3:水平垂直居中
 *      zoom?: number;            // 缩放比
 *      printGridLines?: Boolean; // 是否显示网格线
 *      printFooterPageNum?: Boolean;   // 是否显示页码
 *      hasHomePageCover?: Boolean;     // 首页是否封面
 *      printMode?: number;       // 0:单面打印 1: 双面打印
 *      orientation: number;      // 0:横向 1:纵向
 *    }
 *    .....
 *
 *  const papersizeTypeEnum = [
 *    {label: '信纸', value: 1},
 *    {label: '小纸信', value: 2},
 *    {label: 'Tabloid', value: 3},
 *    {label: 'PAPER LEDGER', value: 4},
 *    {label: '法律专用纸', value: 5},
 *    {label: 'STATEMENT', value: 6},
 *    {label: 'EXECUTIVE', value: 7},
 *    {label: 'A3', value: 8},
 *    {label: 'A4', value: 9},
 *    {label: 'A4_small', value: 10},
 *    {label: 'A5', value: 11},
 *    {label: 'B4', value: 12},
 *    {label: 'B5', value: 13},
 *    {label: 'P80列', value: 45, feetWidth: 9.5, feetHigh: 11 },
 *    {label: 'P40列', value: -45, feetWidth: 10, feetHigh: 5.5},
 *  ]
 */
this.getWidgetRef(#id).allPrint({})
`,
    widgets: ['tablePrint'],
  },
  {
    title: '报表合并打印',
    codeBlock: `/*** 报表打印
 * @param { queryParams }   参数说明见下
*{
*    "param": [
*        {
*            "报表参数key": "报表参数值",
*            "reportCode": "报表code2"  reportCode:报表code
*        },
{
*            "user_id": "报表参数值",
*            "reportCode": "报表code2"  reportCode:报表code
*        }
*    ]
*
*}

 */
this.getWidgetRef(#id).allPrintList({param:[{"报表参数名":"报表code私有参数值","reportCode":"报表code1"},{"user_id":"报表code2私有参数值","reportCode":"报表code2"}]})
`,
    widgets: ['tablePrint'],
  },
  {
    title: '打印',
    codeBlock: 'this.getWidgetRef(#id).print()',
    widgets: ['codeConfig'],
  },
  {
    title: '打开弹窗',
    codeBlock: `this.getWidgetRef(#id).setDialogVisible(true, () => {
  // 可以通过return的形式设置弹窗中表单组件的值
  // object 由表单组件的组件别名组成的键值对
  // return {}
})`,
    widgets: ['dialog'],
  },
  {
    title: '关闭弹窗',
    codeBlock: 'this.getWidgetRef(#id).setDialogVisible(false, () => {})',
    widgets: ['dialog'],
  },
  {
    title: '设置子组件值',
    codeBlock: `/**
 * 设置容器中表单组件的值(可在setDialogVisible中进行return以调用)
 * @param { data } Object 由表单组件的组件别名组成的键值对
 * @param { flag } Boolean data中不存在的key是否清空值, 默认为true
 */
this.getWidgetRef(#id).setCategoryValue({})
`,
    widgets: ['dialog', 'grid'],
  },
  {
    title: '检验子组件内容',
    codeBlock: `/**
 * 检验子组件内容
 * data 父组件内字段的对象
 * msg 失败提示内容和失败字段
 */
this.getWidgetRef(#id).validate().then(data => {
  console.log(data)
}).catch(msg => {
  console.error(msg)
})
`,
    widgets: ['dialog', 'grid'],
  },

  {
    title: '清空子组件内容',
    codeBlock: 'this.getWidgetRef(#id).clear()',
    widgets: ['dialog', 'grid'],
  },
  {
    title: '清除校验提示',
    codeBlock: 'this.getWidgetRef(#id).clearValidate()',
    widgets: ['dialog', 'grid'],
  },
  {
    title: '获取页码',
    codeBlock: 'this.getWidgetRef(#id).getPageNum()',
    widgets: ['data-table'],
  },
  {
    title: '获取单页数据量',
    codeBlock: 'this.getWidgetRef(#id).getPageSize()',
    widgets: ['data-table'],
  },
  {
    title: '设置表格选中的值(多选)',
    codeBlock: `/**
 * 设置表格选中的值
 * @param { rowIds } Array 由表格数据中的主键组成的一维数组
 */
this.getWidgetRef(#id).setCurrentSelectData(rowIds)
`,
    widgets: ['data-table'],
  },
  {
    title: '重置组件值',
    codeBlock: `/**
 * 设置表格选中的值
 * @param { flag } Boolean 是否校验 默认false 不检验
 */
this.getWidgetRef(#id).resetField()
`,
    widgets: ['input'],
  },
  // {
  //   title: '获取下拉框的值(string)',
  //   codeBlock: 'this.getWidgetRef(#id).getSelectValue()',
  //   widgets: ['select', 'select-page'],
  // },
  {
    title: '设置下拉框的值',
    codeBlock: `/**
 * 设置下拉框的值
 * @param { val } Array 例: [1,2]  '1,2'  '1, 2'   支持以上三种格式
 */
this.getWidgetRef(#id).setSelectValue(val)
`,
    widgets: ['select', 'select-page'],
  },
  {
    title: '设置左侧默认选中',
    codeBlock: 'this.getWidgetRef(#id).setLeftChecked(val)',
    widgets: ['transfer'],
  },
  {
    title: '设置右侧默认选中',
    codeBlock: 'this.getWidgetRef(#id).setRightChecked(val)',
    widgets: ['transfer'],
  },
  {
    title: '设置数据源',
    codeBlock: 'this.getWidgetRef(#id).setData(val)',
    widgets: ['transfer'],
  },
  {
    title: '图表初始化',
    codeBlock: 'this.getWidgetRef(#id).initChart(option)',
    widgets: ['chart'],
  },
  {
    title: '图表显示空布局',
    codeBlock: 'this.getWidgetRef(#id).showEmpty()',
    widgets: ['chart'],
  },
  {
    title: '设置是否禁用',
    codeBlock: 'this.getWidgetRef(#id).setIsDisable(flag)',
    widgets: ['dialog'],
  },
  {
    title: '获取表格数据',
    codeBlock: 'this.getWidgetRef(#id).getData()',
    widgets: ['data-table'],
  },
  {
    title: '设置树的选中',
    codeBlock: `/**
 * 设置树的选中
 * @param { val } Array 传入由node-key组成的一维数组,空数组则为清空
 */
this.getWidgetRef(#id).setCheckedKeys(val)
`,
    widgets: ['custom-tree'],
  },
  {
    title: '获取树的选中',
    codeBlock: 'this.getWidgetRef(#id).getCheckedKeys()',
    widgets: ['custom-tree'],
  },
  {
    title: '获取选中简单对象',
    codeBlock: `
this.getWidgetRef(#id).getCascaderCheckItem()
`,
    widgets: ['cascader'],
  },
  {
    title: '获取选中对象',
    codeBlock: `
this.getWidgetRef(#id).getCascaderCheckObjItem()
`,
    widgets: ['cascader'],
  },
  {
    title: '新增一行到表头',
    codeBlock: `/**
 * 新增一行到表头
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).addSubFormFirstRow(data).then(() => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '新增一行到表尾',
    codeBlock: `/**
 * 新增一行到表尾
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).addSubFormLastRow(data).then(() => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '新增一行数据',
    codeBlock: `/**
 * 新增一行数据
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 * @param { index } Number 新增到那个位置, index下标
 */
this.getWidgetRef(#id).addSubFormRow(data, index).then(rowId => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '修改一行数据',
    codeBlock: `/**
 * 修改一行数据
 * @param { index } Number 新增到那个位置， index下标
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).setSubFormRow(index, data).then(rowId => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '删除一行数据',
    codeBlock: `/**
 * 删除一行数据
 * @param { index } Number index下标
 */
this.getWidgetRef(#id).delSubFormRow(index)
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '清空子表单数据',
    codeBlock: `this.getWidgetRef(#id).clearFromRowIdData()`,
    widgets: ['sub-form'],
  },
  {
    title: '前进',
    codeBlock: `this.getWidgetRef(#id).advance()`,
    widgets: ['steps'],
  },
  {
    title: '后退',
    codeBlock: `this.getWidgetRef(#id).back()`,
    widgets: ['steps'],
  },
  {
    title: '获取当前tab项',
    codeBlock: `this.getWidgetRef(#id).getCurrentTab()`,
    widgets: ['tab'],
  },
  {
    title: '通过fromId设置视图',
    codeBlock: `this.getWidgetRef(#id).setFormId(formId, {})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '通过templateId设置视图',
    codeBlock: `this.getWidgetRef(#id).setTemplateId(templateId, {})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '校验模板内容并获取值',
    codeBlock: `this.getWidgetRef(#id).validate().then(data => {})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '设置列隐藏',
    codeBlock: `/**
 * 设置列隐藏
 * @param { prop } String 列的prop值
 * @param { state } Boolean   true: 隐藏   false: 显示
 */
this.getWidgetRef(#id).setColHidden(prop, state)`,
    widgets: ['data-table'],
  },
  {
    title: '设置分页单页数据量组',
    codeBlock: `this.getWidgetRef(#id).setPageSizes([20,30,40,50])`,
    widgets: ['data-table'],
  },
  {
    title: '设置行编辑状态',
    codeBlock: `/**
 * 设置行编辑状态
 * @param { rowKey } String 主键
 * @param { flag } Boolean  true:编辑状态  false:非编辑状态
 * @param { callback } Function 当状态为false时传入回调函数, obj为编辑后的行数据
 */
this.getWidgetRef(#id).setRowEditable(rowKey, flag, (obj) => {})`,
    widgets: ['data-table'],
  },

  {
    title: '获取展开行内容对象',
    codeBlock: `this.getWidgetRef(#id).getSubTable(0)`,
    widgets: ['data-table'],
  },
  {
    title: '请求数据绑定接口',
    codeBlock: `this.getWidgetRef(#id).sendApi()`,
    widgets: [
      'cascader',
      'select',
      'checkbox',
      'custom-condition',
      'data',
      'descriptions',
      'dropdown',
      'radio',
      'transfer',
      'tree',
    ],
  },
  {
    title: '图表显示loading',
    codeBlock: `this.getWidgetRef(#id).showLoading()`,
    widgets: ['chart'],
  },
  {
    title: '图表关闭loading',
    codeBlock: `this.getWidgetRef(#id).hideLoading()`,
    widgets: ['chart'],
  },
  {
    title: '获取所引用组件的实例',
    codeBlock: `/**
* @param { name } String  所引用的在线组件的 name 名称
* 注意: 所引用的在线组件的data中必须要有个属性为onlineFlag,值随意
*/
this.getWidgetRef(#id).getCodeWidget(name).then(res => {
  // res为引用组件的ref实例

})`,
    widgets: ['onlineComponents'],
  },

  {
    title: '设置表格展开的行',
    codeBlock: `/**
* 设置表格展开的行
* @param { data } Array  由行数据的key组成的一维数组
*/
this.getWidgetRef(#id).setExpandRow(data)`,
    widgets: ['data-table'],
  },
  {
    title: '生成编码',
    codeBlock: `
this.getWidgetRef(#id).buildNumber()`,
    widgets: ['order-input'],
  },
  {
    title: '设置',
    codeBlock: `
this.getWidgetRef(#id).buildNumber()`,
    widgets: ['order-input'],
  },
  {
    title: '设置科室(当前登录人)的值',
    codeBlock: `
// id Number 科室(当前登录人)的id
this.getWidgetRef(#id).fieldModel = id`,
    widgets: ['deptinput', 'userinput'],
  },
  {
    title: '获取已选字段',
    codeBlock: `this.getWidgetRef(#id).getSelectedFields()`,
    widgets: ['transfer'],
  },
  {
    title: '获取备选字段',
    codeBlock: `this.getWidgetRef(#id).getUnSelectedFields()`,
    widgets: ['transfer'],
  },
  {
    title: '设置迭代次数',
    codeBlock: `/**
* @param { data } array 根据数组长度进行遍历的
*/
this.getWidgetRef(#id).setIterationCount(data)`,
    widgets: ['iteration'],
  },
  {
    title: '设置卡片展开收缩',
    codeBlock: `/**
 * @param { val } Boolean  true: 展开  false: 收缩
 */
this.getWidgetRef(#id).toggleState(val)`,
    widgets: ['card'],
  },
  {
    title: '设置底部按钮加载状态',
    codeBlock: `/**
 * 设置底部按钮加载状态
 * @param { index }  Number  下标
 * @param { state }  Boolean 状态 true:加载 false:取消
 */
this.getWidgetRef(#id).setLoading(index, state)`,
    widgets: ['dialog'],
  },
  {
    title: '图表重新调整大小',
    codeBlock: 'this.getWidgetRef(#id).resize()',
    widgets: ['chart'],
  },
  {
    title: '表格主动触发onEditChange',
    codeBlock: `/**
 * 表格主动触发onEditChange
 * @param { val } String|Number  值
 * @param { fieldName } String   字段名
 * @param { rowIndex } Number    所在的行索引
 * @param { colIndex } Number    所在的列索引
 * @param { rowData }  Object    行数据
 * @param { colData }  Object    列的相对属性
 * @param { extra }    Object    额外参数
 * 传什么 onEditChange 那边就接到什么
 */
this.getWidgetRef(#id).triggerEditChange(val, fieldName, rowIndex, colIndex, rowData, colData, extra)`,
    widgets: ['data-table'],
  },
  {
    title: '设置编辑列不可编辑关系',
    codeBlock: `/**
 * 设置编辑列不可编辑关系
 * @param { id } Number|String  当前编辑数据的主键
 * @param { relation } Object  {prop: Boolean} 组成的键值对
 *
 * 例如: {name: true}
 *      就表示name这个字段在当前情况是不能编辑的(禁用,不参与编辑)
 */
this.getWidgetRef(#id).setDisableRelation(id, relation)`,
    widgets: ['data-table'],
  },
  {
    title: '获取树的数据源',
    codeBlock: `this.getWidgetRef(#id).getDataSource()`,
    widgets: ['custom-tree'],
  },
  {
    title: '设置树的数据源',
    codeBlock: `/**
 * 设置树的数据
 * @param { data } Array  树的数据源
 */
this.getWidgetRef(#id).setDataSource(data)`,
    widgets: ['custom-tree'],
  },
  {
    title: '设置条码内容',
    codeBlock: `/**
 * 设置条码内容
 * @param { data } String  条码内容
 */
this.getWidgetRef(#id).initBarcode(data)`,
    widgets: ['barCode'],
  },
  {
    title: '获取下拉框当前的所有选项',
    codeBlock: `this.getWidgetRef(#id).getSelectData()`,
    widgets: ['select'],
  },
  {
    title: '刷新数据来源',
    codeBlock: `this.getWidgetRef(#id).refresh()`,
    widgets: ['tree'],
  },
  {
    title: '设置容器下所有子节点的属性',
    codeBlock: `/**
 * 设置容器下所有子节点的属性
 * @param { data } config  Object
 * 例如: { disabled: true } 设置所有节点为禁用
 */
this.getWidgetRef(#id).setGridAllNode(config)`,
    widgets: ['grid'],
  },
  {
    title: '设置样式',
    codeBlock: `this.getWidgetRef(#id).setStyle({
  color: 'red'
})`,
    widgets: ['static-text'],
  },
  {
    title: '设置显隐',
    codeBlock: `/**
 * @param { flag }  Boolean
 * @param { callback }  Function
 */
this.getWidgetRef(#id).setVisible(flag,callback)`,
    widgets: ['drawer'],
  },
  {
    title: '组件主动聚焦',
    codeBlock: 'this.getWidgetRef(#id).focus()',
    widgets: ['input'],
  },
  {
    title: '设置弹窗加载状态',
    codeBlock: `/**
    * @param { flag }  Boolean
    */
   this.getWidgetRef(#id).setDialogLoading(flag)`,
    widgets: ['dialog'],
  },


];

/**
 * 根据组件的type值来决定有哪些右键菜单
 * @param {*} type
 */
let getContextMenu = (type) => {
  return menuData.filter(
    (item) => item.widgets === true || item.widgets.includes(type),
  );
};

export { getContextMenu };
