<template>
  <v-contextmenu ref="contextMenuRef" style="max-height:300px;overflow:auto">
    <v-contextmenu-item v-for="(item, index) in menuList" :key="index">
      <div @click="analyticExpression($event, item.codeBlock)">
        {{ item.title }}
      </div>
    </v-contextmenu-item>
  </v-contextmenu>
</template>

<script>
import { getContextMenu } from './contextMenuConfig';
import { copyToClipboard } from '@/utils/util';
export default {
  data() {
    return {
      menuList: [],
      currentData: {},
    };
  },
  methods: {
    show(data) {
      this.currentData = data.data.data;
      this.menuList = getContextMenu(this.currentData.type);
      if (!this.menuList.length) return;
      this.$refs.contextMenuRef.show(data);
      setTimeout(() => {
        document.addEventListener('click', this.hide);
      }, 10);
    },
    hide() {
      document.removeEventListener('click', this.hide);
      this.$refs.contextMenuRef.hide();
    },
    /**
     * 解析表达式
     * @param {*} codeBlock
     */
    analyticExpression(e, codeBlock) {
      if (!codeBlock) {
        this.$message({ message: '代码块不能为空' });
        return;
      }
      codeBlock = codeBlock.replace(/#id/g, `'${this.currentData.id}'`);
      copyToClipboard(codeBlock, e, this.$message, '复制成功', '复制失败');
    },
  },
};
</script>
<style >
.v-contextmenu{
  z-index:9999
}
</style>
