<template>
  <div>
    <el-form
      :model="formConfig"
      size="mini"
      label-position="left"
      label-width="120px"
      class="setting-form"
      @submit.native.prevent
    >
      <el-collapse v-model="formActiveCollapseNames" class="setting-collapse">
        <el-collapse-item
          name="1"
          :title="i18nt('designer.setting.basicSetting')"
        >
          <el-form-item label="弹窗分组">
            <el-switch @change="groupDialogChange" v-model="groupDialog"></el-switch>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formSize')">
            <el-select v-model="formConfig.size">
              <el-option
                v-for="item in formSizes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据表自定义">
            <el-switch @change="custonChange" v-model="isCustom"></el-switch>
          </el-form-item>
          <el-form-item label="自定义数据表:" v-if="isCustom">
                  <el-input
                    size="mini"
                    placeholder="输入数据表名"
                    v-model="formConfig.dataTable"
                  ></el-input>
                </el-form-item>
          <el-form-item label="绑定数据表" v-else>
            <el-select v-model="formConfig.dataTable" clearable  filterable>
              <el-option
                v-for="item in formTables"
                :key="item.tableComment"
                :label="item.tableName"
                :value="item.tableComment"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="自定义权限项">
            <div>
              <el-button type="text" @click="addAuth">添加权限项</el-button>
            </div>
          </el-form-item>
          <el-form-item label-width="0">
            <li
              v-for="(colItem, colIdx) in formConfig.authoritys"
              :key="colIdx"
              class="col-item"
            >
              <div style="float: right">
                <el-button
                  circle
                  plain
                  size="mini"
                  style="float: right"
                  type="danger"
                  @click="delAuth(colIdx)"
                  icon="el-icon-minus"
                ></el-button>
              </div>
              <el-form label-position="left" inline>
                <el-form-item label="权限名:">
                  <el-input
                    size="mini"
                    placeholder="输入权限项名称"
                    v-model="colItem.authName"
                  ></el-input>
                </el-form-item>
                <el-form-item label="权限值:">
                  <el-input
                    size="mini"
                    placeholder="输入权限项唯一code"
                    v-model="colItem.authCode"
                  ></el-input>
                </el-form-item>
              </el-form>
            </li>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelPosition')">
            <el-radio-group
              v-model="formConfig.labelPosition"
              class="radio-group-custom"
            >
              <el-radio-button label="left">{{
                i18nt('designer.setting.leftPosition')
              }}</el-radio-button>
              <el-radio-button label="top">{{
                i18nt('designer.setting.topPosition')
              }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelAlign')">
            <el-radio-group
              v-model="formConfig.labelAlign"
              class="radio-group-custom"
            >
              <el-radio-button label="label-left-align">{{
                i18nt('designer.setting.leftAlign')
              }}</el-radio-button>
              <el-radio-button label="label-center-align">{{
                i18nt('designer.setting.centerAlign')
              }}</el-radio-button>
              <el-radio-button label="label-right-align">{{
                i18nt('designer.setting.rightAlign')
              }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelWidth')">
            <el-input-number
              v-model="formConfig.labelWidth"
              :min="0"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formCss')">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormCss"
              >{{ i18nt('designer.setting.addCss') }}</el-button
            >
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.customClass')">
            <el-select
              v-model="formConfig.customClass"
              multiple
              filterable
              allow-create
              default-first-option
            >
              <el-option
                v-for="(item, idx) in cssClassList"
                :key="idx"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.globalFunctions')">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editGlobalFunctions"
              >{{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>
          <el-form-item label-width="0">
            <el-divider class="custom-divider">{{
              i18nt('designer.setting.formSFCSetting')
            }}</el-divider>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formModelName')">
            <el-input type="text" v-model="formConfig.modelName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRefName')">
            <el-input type="text" v-model="formConfig.refName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRulesName')">
            <el-input type="text" v-model="formConfig.rulesName"></el-input>
          </el-form-item>
        </el-collapse-item>

        <el-collapse-item
          name="2"
          :title="i18nt('designer.setting.eventSetting')"
        >
          <el-form-item label="表单创建事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormCreated')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>
          <el-form-item label="表单挂载事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormMounted')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>
          <el-form-item label="扫码提交事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onScanCommit')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>
          <!-- -->
          <el-form-item label="表单数据改变事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormDataChange')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>

          <el-form-item label="提交前置方法" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onPreCommit')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>

          <el-form-item label="拦截流程提交" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormCommit')"
            >
              {{ i18nt('designer.setting.addEventHandler') }}</el-button
            >
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <el-dialog
      :title="i18nt('designer.setting.editFormEventHandler')"
      :visible.sync="showFormEventDialogFlag"
      v-if="showFormEventDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
       width="80vw"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="d-flex">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <el-alert
            type="primary"
            :closable="false"
            :title="'form.' + eventParamsMap[curEventName]"
          />
          <code-editor
            :mode="'javascript'"
            :readonly="false"
            v-model="formEventHandlerCode"
          />
          <el-alert type="primary" :closable="false" title="}" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = true">
          {{ i18nt('designer.hint.document') }}</el-button
        >
        <el-button @click="showFormEventDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button
        >
        <el-button type="primary" @click="saveFormEventHandler">
          {{ i18nt('designer.hint.confirm') }}</el-button
        >
      </div>
    </el-dialog>

    <!-- 开发文档   -->
    <el-dialog
      title="开发文档"
      :visible.sync="showEventDocumentDialogFlag"
      v-if="showEventDocumentDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <pre>
        <code>
获取指定组件Vue对象 getWidgetRef(widgetId)  widgetId 唯一Id值
数据表格刷新数据 this.getWidgetRef(widgetId).updateRefresh() widgetId 对应数据表的Id
获取组件值  this.getWidgetRef(widgetId).getValue() widgetId 对应数据表的Id,仅对表单组件生效
设置组件值(不会触发onChange this.getWidgetRef(widgetId).setValue(value)  widgetId 对应数据表的Id, value 设置的值
改变组件值(触发onChange) changeValue()
发送http请求 this.getRequest({
  url: 'xxxxxxx',
  method: 'post',
  data: {
    name: 'xxxxx',
    age: 'xxxxx'
  },
  params: {
    name: '12312312'
  }
}).then(res => {
  console.log(res)
}).catch(err => {
  console.log(err)
})
初始化图表数据 this.getWidgetRef(widgetId).initChart(obj)  obj echarts配置项
设置弹窗是否显示 this.getWidgetRef(widgetId).setDialogVisible(bol)   bol 显示与否， true 显示，false 隐藏
消息提示 this.$message({message: "提示内容", type:"success"}) 系统弹窗消息提示
        </code>
      </pre>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      :title="i18nt('designer.setting.formCss')"
      :visible.sync="showEditFormCssDialogFlag"
      center
      v-if="showEditFormCssDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <code-editor
        :mode="'css'"
        :readonly="false"
        v-model="formCssCode"
      ></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = true">
          {{ i18nt('designer.hint.document') }}</el-button
        >
        <el-button @click="showEditFormCssDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button
        >
        <el-button type="primary" @click="saveFormCss">
          {{ i18nt('designer.hint.confirm') }}</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.setting.globalFunctions')"
      :visible.sync="showEditFunctionsDialogFlag"
      v-if="showEditFunctionsDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      width="80vw"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="d-flex">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <code-editor
            :mode="'javascript'"
            :readonly="false"
            v-model="functionsCode"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = true">
          {{ i18nt('designer.hint.document') }}</el-button
        >
        <el-button @click="showEditFunctionsDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button
        >
        <el-button type="primary" @click="saveGlobalFunctions">
          {{ i18nt('designer.hint.confirm') }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryFlowDataTable } from '@/api/tool/form';
import i18n from '@/utils/i18n';
import CodeEditor from '@/components/code-editor/index.vue';
import bus from '@/scripts/bus'
import {
  deepClone,
  insertCustomCssToHead,
  insertGlobalFunctionsToHtml,
} from '@/utils/util';
import componentTree from './components/componentTree.vue';

export default {
  name: 'form-setting',
  mixins: [i18n],
  components: {
    CodeEditor,
    componentTree,
  },
  props: {
    designer: Object,
    formConfig: Object,
    str: String,
  },
  data() {
    return {
      isCustom:false,
      groupDialog:true,
      formActiveCollapseNames: ['1', '2'],
      formSizes: [
        { label: 'default', value: '' },
        { label: 'large', value: 'large' },
        { label: 'medium', value: 'medium' },
        { label: 'small', value: 'small' },
        { label: 'mini', value: 'mini' },
      ],
      formTables: [],
      showEditFormCssDialogFlag: false,
      formCssCode: '',
      cssClassList: [],

      showEditFunctionsDialogFlag: false,
      functionsCode: '',

      showFormEventDialogFlag: false,
      showEventDocumentDialogFlag: false,
      formEventHandlerCode: '',
      curEventName: '',

      eventParamsMap: {
        onFormCreated: 'onFormCreated() {',
        onFormMounted: 'onFormMounted() {',
        onFormDataChange:
          'onFormDataChange(fieldName, newValue, oldValue, formModel, subFormName, subFormRowIndex) {',
        onFormCommit: 'onFormCommit(commitData, historyCommitData, callback) {',
        onPreCommit: 'onFormCommit(commitType,hasCommitForm) {',
        onScanCommit: 'onScanCommit(commitCode) {',

        //'onFormValidate':     'onFormValidate() {',
      },
    };
  },

  created() {
    //导入表单JSON后需要重新加载自定义CSS样式！！！
    this.designer.handleEvent('form-json-imported', () => {
      this.formCssCode = this.formConfig.cssCode;
      insertCustomCssToHead(this.formCssCode);
      this.extractCssClass();
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    });
  },
  mounted() {
    /* SettingPanel和FormWidget为兄弟组件, 在FormWidget加载formConfig时，
         此处SettingPanel可能无法获取到formConfig.cssCode, 故加个延时函数！ */
    setTimeout(() => {
      this.formCssCode = this.formConfig.cssCode;
      insertCustomCssToHead(this.formCssCode);
      this.extractCssClass();
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    }, 1200);
    // 获取数据表格列表
    this.flowDataTable();


  },
  methods: {
    groupDialogChange() {
      bus.$emit('onVFromGroupDialogChange', this.groupDialog);
    },
    custonChange(e){
      if(e){
        this.formConfig.dataTable='flow_bus_'
      }else{
        this.formConfig.dataTable=null
      }
    },
    async copyBoard() {
        let url = this.str; //拿到想要复制的值
        let copyInput = document.createElement("input"); //创建input元素
        document.body.appendChild(copyInput); //向页面底部追加输入框
        copyInput.setAttribute("value", url); //添加属性，将url赋值给input元素的value属性
        copyInput.select(); //选择input元素
        document.execCommand("Copy"); //执行复制命令
        this.$message.success("复制成功"); //弹出提示信息，不同组件可能存在写法不同
        //复制之后再删除元素，否则无法成功赋值
        copyInput.remove(); //删除动态创建的节点
    },
    // 新增权限项目
    addAuth() {
      if (
        this.designer.formConfig.authoritys == '' ||
        this.designer.formConfig.authoritys == null
      ) {
        this.designer.formConfig.authoritys = [{ authName: '', authCode: '' }];
      } else {
        this.designer.formConfig.authoritys.push({
          authName: '',
          authCode: '',
        });
      }
    },
    // 删除权限项目
    delAuth(index) {
      this.designer.formConfig.authoritys.splice(index, 1);
    },
    flowDataTable() {
      queryFlowDataTable().then((res) => {
        this.formTables = res.data;
      });
    },
    editFormCss() {
      this.formCssCode = this.designer.formConfig.cssCode;
      this.showEditFormCssDialogFlag = true;
    },

    extractCssClass() {
      let regExp = /\..*{/g;
      let result = this.formCssCode.match(regExp);
      let cssNameArray = [];

      if (!!result && result.length > 0) {
        result.forEach((rItem) => {
          let classArray = rItem.split(','); //切分逗号分割的多个class
          if (classArray.length > 0) {
            classArray.forEach((cItem) => {
              let caItem = cItem.trim();
              if (caItem.indexOf('.', 1) !== -1) {
                //查找第二个.位置
                let newClass = caItem.substring(
                  caItem.indexOf('.') + 1,
                  caItem.indexOf('.', 1),
                ); //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else if (caItem.indexOf(' ') !== -1) {
                //查找第一个空格位置
                let newClass = caItem.substring(
                  caItem.indexOf('.') + 1,
                  caItem.indexOf(' '),
                ); //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else {
                if (caItem.indexOf('{') !== -1) {
                  //查找第一个{位置
                  let newClass = caItem.substring(
                    caItem.indexOf('.') + 1,
                    caItem.indexOf('{'),
                  );
                  cssNameArray.push(newClass.trim());
                } else {
                  let newClass = caItem.substring(caItem.indexOf('.') + 1);
                  cssNameArray.push(newClass.trim());
                }
              }
            });
          }
        });
      }

      //this.cssClassList.length = 0
      this.cssClassList.splice(0, this.cssClassList.length); //清除数组必须用splice，length=0不会响应式更新！！
      this.cssClassList = Array.from(new Set(cssNameArray)); //数组去重
    },

    saveFormCss() {
      this.extractCssClass();
      this.designer.formConfig.cssCode = this.formCssCode;
      insertCustomCssToHead(this.formCssCode);
      this.showEditFormCssDialogFlag = false;

      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    },

    editGlobalFunctions() {
      this.functionsCode = this.designer.formConfig.functions;
      this.showEditFunctionsDialogFlag = true;
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },

    saveGlobalFunctions() {
      this.designer.formConfig.functions = this.functionsCode;
      insertGlobalFunctionsToHtml(this.functionsCode);
      this.showEditFunctionsDialogFlag = false;
    },

    editFormEventHandler(eventName) {
      this.curEventName = eventName;
      this.formEventHandlerCode = this.formConfig[eventName];
      this.showFormEventDialogFlag = true;
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },

    saveFormEventHandler() {
      this.formConfig[this.curEventName] = this.formEventHandlerCode;
      this.showFormEventDialogFlag = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-form {
  li.col-item {
    list-style: none;

    margin: 5px 0;
  }
  ::v-deep .el-form-item__label {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 6px;
  }

  .radio-group-custom {
    ::v-deep .el-radio-button__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .custom-divider.el-divider--horizontal {
    margin: 10px 0;
  }
}

.setting-collapse {
  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
  }

  ::v-deep .el-collapse-item__header {
    font-style: italic;
    font-weight: bold;
  }
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 0 15px;
  }
}
::v-deep .el-dialog {
  // width: 70% !important;
}
</style>
