<template>
  <el-form-item :label="i18nt('designer.setting.paramConfig')">
    <!-- <el-switch v-model="optionModel.paramConfig" /> -->
    <el-button size="mini" type="primary" round plain style="width: 100%" @click="openConfig"
      >配置</el-button
    >
    <paramDialog
      ref="paramDialogRef"
      @close="close"
      :allFormItem="allFormItem"
    />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import paramDialog from '@/views/activity/ef/nodeForm/components/param-dialog.vue';
export default {
  name: 'ParamConfigEditor',
  components: {
    paramDialog,
  },
  mixins: [i18n],
  data() {
    return {
      allFormItem: [],
    };
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    openConfig() {
      let options = this.$parent.$children.find(
        (item) => item.$options.name === 'ApiEditor',
      ).options;
      if (!this.selectedWidget.options.api) {
        this.$message('请先选择绑定api');
        return;
      }

      let itemObj = {};
      let dfs = (root) => {
        if (root.interfaceId === this.selectedWidget.options.api) {
          itemObj = root;
          return;
        }
        root.children && root.children.forEach((item) => dfs(item));
      };
      dfs({ children: options });

      if (
        (itemObj.methodType === 'GET' && !itemObj.parameters) ||
        (itemObj.methodType !== 'GET' && !itemObj.requestBody)
      ) {
        this.$message.error('接口不存在入参');
        return;
      }

      let allFormItem = [
        {
          elementTitle: '当前表单',
          items: [],
          templateId: '固定值',
          elementKey: '固定值',
        },
      ]; // 所有的表单项
      let dfs2 = (root) => {
        root.forEach((item) => {
          const {
            id,
            type,
            options: { label, name },
          } = item;

          allFormItem[0].items.push({
            elementKey: id,
            elementType: type,
            elementTitle: label ? `${label} (${name})` : name,
            templateId: '固定值',
          });
          if ('widgetList' in item) {
            item.widgetList.forEach((item2) => {
              if (
                !item2.id.includes('.') &&
                !item.id.includes('card') &&
                !item.id.includes('grid') &&
                !item.id.includes('condition-container-body') &&
                !item.id.includes('condition-container-footer')
              ) {
                item2.id = item.id + '.' + item2.id;
              }
            });
            dfs2(item.widgetList);
          }
          if ('cols' in item) {
            dfs2(item.cols);
          }
          if ('tabs' in item) {
            dfs2(item.tabs);
          }
        });
      };

      dfs2(JSON.parse(JSON.stringify(this.designer.widgetList)));
      this.allFormItem = allFormItem;
      let apiParam = {};
      if (itemObj.parameters != '{}' && itemObj.parameters != '') {
        apiParam = JSON.parse(itemObj.parameters);
      } else if (itemObj.requestBody != null || itemObj.requestBody != '') {
        apiParam = JSON.parse(itemObj.requestBody);
      }

      // 去除掉一些固定参数 ，比如：分页的pageSize和pageNum
      ['pageSize', 'pageNum', 'choiceId'].map((item) => {
        delete apiParam[item];
      });
      this.$refs.paramDialogRef.open(
        JSON.stringify(apiParam),
        this.optionModel.paramConfig
          ? JSON.parse(this.optionModel.paramConfig)
          : [],
      );
    },
    close(e) {
      this.optionModel.paramConfig = JSON.stringify(e.data);
    },
  },
};
</script>

<style scoped></style>
