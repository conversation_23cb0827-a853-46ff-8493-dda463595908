<template>
  <el-form-item label="在线组件关联">
    <el-select
      v-model="optionModel.onlineRelevance"
      placeholder="请选择在线组件"
    >
      <el-option
        v-for="item in reportData"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import { getAssemblyList } from '@/api/interfaces/interfaces';

export default {
  name: 'online-relevance-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      reportData: [],
    };
  },
  created() {
    getAssemblyList({
      pageNum: 1,
      pageSize: 9999,
    }).then((response) => {
      this.reportData = response.data.records;
    });
  },
};
</script>

<style lang="scss" scoped></style>
