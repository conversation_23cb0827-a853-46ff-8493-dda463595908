<template>
  <el-form-item :label="label">
    <el-input-number v-model="optionModel.rows" style="width: 100%"></el-input-number>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "rows-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    computed:{
      label(){
        if(this.selectedWidget.type=='descriptions'){
          return '列数'
        }else{
          return '行数'
        }
      }
    }

  }
</script>

<style scoped>

</style>
