<template>
  <el-form-item :label="i18nt('designer.setting.dialogTable')">
    <el-select v-model="optionModel.dialogTable" placeholder="请选择">
      <el-option
        v-for="item in getPopupItems()"
        :key="item.type"
        :label="item.title"
        :value="item.type">
      </el-option>
    </el-select>
  </el-form-item>

</template>

<script>
  import i18n from "@/utils/i18n"
  import { containers } from "@/components/form-render/popup-item/popupConfig"
  export default {
    name: "dialogTable-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data: {
      containers
    },
    methods: {
      getPopupItems() {
        console.log(containers)
        return [...containers]
      }
    }
  }
</script>

<style scoped>

</style>
