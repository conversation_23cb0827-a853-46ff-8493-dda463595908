<template>
  <div>
    <el-form-item label="树表" v-if="!optionModel.isEnable">
      <el-switch
        v-model="optionModel.treeTable"
        class="hide-spin-button"
        style="width: 100%"
      ></el-switch>
    </el-form-item>
    <el-form-item v-if="optionModel.treeTable" label="节点ID">
      <el-input type="text" v-model="optionModel.rowKey"></el-input>
    </el-form-item>
    <el-form-item v-if="optionModel.treeTable" label="子集字段">
      <el-input type="text" v-model="optionModel.subsetField"></el-input>
    </el-form-item>
    <el-form-item v-if="optionModel.treeTable" label="父子联动">
      <el-switch v-model="optionModel.linkAble"></el-switch>
    </el-form-item>
    <el-form-item v-if="optionModel.treeTable" label="展开所有行">
      <el-switch v-model="optionModel.defaultExpandAll"></el-switch>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
export default {
  name: 'treeTable-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      dialogFlag: false,
    };
  },
};
</script>

<style scoped></style>
