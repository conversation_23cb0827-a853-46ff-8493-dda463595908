<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.activeText')">
      <el-input v-model="optionModel.activeText"></el-input>
    </el-form-item>
    <el-form-item label="开启时值">
      <el-input v-model="optionModel.activeValue"></el-input>
    </el-form-item>
  </div>
  
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "activeText-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
