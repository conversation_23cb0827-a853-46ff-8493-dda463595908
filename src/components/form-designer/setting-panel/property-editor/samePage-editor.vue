<template>
  <el-form-item :label="i18nt('designer.setting.samePage')">
    <el-switch v-model="optionModel.samePage" @change="change"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "samePage-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
     methods:{
      change(e){
        // samePage 和 groupPage只能选择一个
        if(this.optionModel.groupPage) {
          this.optionModel.groupPage= false
        }
      }

    }
  }
</script>

<style scoped>

</style>
