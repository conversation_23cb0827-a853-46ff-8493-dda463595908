<template>
  <div>
    <el-form-item
      label="开启复选框"
    >
      <el-switch
        v-model="optionModel.showCheckbox">
      </el-switch>
    </el-form-item>
  </div>

</template>

<script>
import i18n from '@/utils/i18n'
export default {
  name: 'showCheckbox-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {}
  },
  watch: {
  },
  created() {},
  methods: {
  },
}
</script>
