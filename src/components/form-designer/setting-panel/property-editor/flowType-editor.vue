<template>
  <el-form-item :label="i18nt('designer.setting.flowType')" v-if="!noLabelSetting">
    <el-select v-model="optionModel.flowType" placeholder="请选择">
      <el-option v-for="item in busType" :key="item.functionalId" :label="item.functionalName" :value="item.functionalId">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import request from '@/utils/request'
  export default {
    name: "flowType-editor",
    mixins: [i18n],
    data() {
    return {
      busType:[]
    }
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    mounted:function(){
      this.getBusType();
    },
    methods:{
      getBusType(){
        return request({
          url: '/flows/processModel/busByFunctional',
          method: 'get',
          params: {busId:1}
        }).then(res=>{
          this.busType=res.data
        })
      }
    },
    computed: {
      noLabelSetting() {
        return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
        //|| (this.selectedWidget.type === 'divider')
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
