<template>
    <div>
        <el-form-item label="最大页码按钮数">
            <el-input type="number" v-model="optionModel.pagerCount" :min="3" />
        </el-form-item>
    </div>
</template>
    
<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
export default {
    name: 'pagerCount-editor',
    mixins: [i18n, propertyMixin],
    props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
    },
    watch: {
        optionModel: {
            immediate: true,
            handler(val) {
                // 如果没有这个字段则添加这个字段
                if (val.pagerCount == null) {
                    this.$set(this.optionModel, "pagerCount", 7)
                }
            },
            deep: true,
        },
    },
}
</script>
    
<style scoped></style>
    