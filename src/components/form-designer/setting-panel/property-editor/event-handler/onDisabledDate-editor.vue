<template>
  <el-form-item label="日期禁用事件" title="onDisabledDate" label-width="150px">
    <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('onDisabledDate', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "onDisabledDate-editor",
    mixins: [i18n, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        eventParams: ['value', 'time',],
      }
    }
  }
</script>

<style scoped>

</style>
