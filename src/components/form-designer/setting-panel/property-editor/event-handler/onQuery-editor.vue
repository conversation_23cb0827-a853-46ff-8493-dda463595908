<template>
  <el-form-item label="查询点击事件" title="onQuery" label-width="150px">
    <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('onQuery', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "onQuery-editor",
    mixins: [i18n, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        eventParams: ['conditions'],
      }
    }
  }
</script>

<style scoped>

</style>
