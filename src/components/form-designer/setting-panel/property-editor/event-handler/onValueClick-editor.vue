<template>
  <el-form-item label="点击右侧值" title="onValueClick" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onValueClick', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onValueClick-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ['item', 'index'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!'onValueClick' in val) {
          this.$set(this.optionModel, 'onValueClick', '');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
