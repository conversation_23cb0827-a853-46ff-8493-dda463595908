<template>
  <el-form-item label="选中菜单项事件" title="onCommand" label-width="150px">
    <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('onCommand', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "onCommand-editor",
    mixins: [i18n, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        eventParams: ['command'],
      }
    }
  }
</script>

<style scoped>

</style>
