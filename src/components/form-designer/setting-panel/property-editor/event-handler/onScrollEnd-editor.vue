<template>
  <el-form-item label="滚动条触底事件" title="onScrollEnd" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onScrollEnd', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onScrollEnd-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: [],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onScrollEnd == null || val.onScrollEnd === '') {
          this.$set(this.optionModel, 'onScrollEnd', '');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
