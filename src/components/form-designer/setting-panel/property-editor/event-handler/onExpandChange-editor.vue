<template>
  <el-form-item label="展开行改变事件" title="onExpandChange" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onExpandChange', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onExpandChange-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ['row', 'expandedRows'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onExpandChange == null) {
          this.$set(
            this.optionModel,
            'onExpandChange',
            `/*
  row: 当前展开(关闭)的行
  expandedRows: 当前所有展开的行
*/
`,
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
