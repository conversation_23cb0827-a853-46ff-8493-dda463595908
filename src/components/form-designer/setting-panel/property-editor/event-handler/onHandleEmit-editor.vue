<template>
  <el-form-item label="接收事件" title="onHandleEmit" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onHandleEmit', eventParams)"
    >
      编写代码</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onHandleEmit-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ['e'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onHandleEmit == null) {
          this.$set(
            this.optionModel,
            'onHandleEmit',
            `/*
  接收在线组件通过dispatch传递出来的参数
*/
`,
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
