<template>
  <el-form-item label="行内编辑改变事件" title="onEditChange" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onEditChange', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onEditChange-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: [
        'val',
        'fieldName',
        'rowIndex',
        'colIndex',
        'rowData',
        'colData',
        'extra',
      ],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onEditChange == null || val.onEditChange === '') {
          this.$set(
            this.optionModel,
            'onEditChange',
            `/*
  表格开启行内编辑后input的change回调
    val: String|Number,  // 值
    fieldName: String,   // 字段名
    rowIndex: Number     // 所在的行索引
    colIndex: Number     // 所在的列索引
    rowData: Object      // 行数据
    colData: Object      // 列的相对属性
    extra: Object        // 额外参数
*/
`,
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
