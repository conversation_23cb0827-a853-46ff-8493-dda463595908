<template>
  <el-form-item label="表头合并样式" title="onRowClass" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onRowClass', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onRowClass-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ["row", "column", "rowIndex", "columnIndex"],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.onRowClass || val.onRowClass === "") {
          this.$set(
            this.optionModel,
            "onRowClass",
            `//  当前行row、当前列column、当前行号rowIndex、当前列号columnIndex 该函数可以返回一个包含两个元素的数组，第一个元素代表rowspan，第二个元素代表colspan。 也可以返回一个键名为rowspan和colspan的对象。
// 实例 将第一二列合并成一列
//if(rowIndex===0 && columnIndex===0) {
//   this.$nextTick(()=> {
//     if(document.getElementsByClassName(column.id).length!==0) {
//       document.getElementsByClassName(column.id)[0].setAttribute('rowSpan',2);
//       return false
//     }
//   })
// }
//if(rowIndex===1 && (columnIndex===0 || columnIndex===1)) {
//  return {display:'none'}
//}
            `
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped>
</style>
