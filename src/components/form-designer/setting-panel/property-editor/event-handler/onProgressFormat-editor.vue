<template>
    <el-form-item label="文字内容" title="onProgressFormat" label-width="150px">
        <el-button type="primary" icon="el-icon-edit" plain round
            @click="editEventHandler('onProgressFormat', eventParams)">
            {{ i18nt('designer.setting.addEventHandler') }}</el-button>
    </el-form-item>
</template>
  
<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
    name: 'onProgressFormat-editor',
    mixins: [i18n, eventMixin],
    props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
    },
    data() {
        return {
            eventParams: ['percentage'],
        };
    },
};
</script>
  
<style scoped></style>
  