<template>
  <el-form-item label="创建选项事件" title="allowCreateFn" label-width="150px">
    <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('allowCreateFn', eventParams)">
      {{ i18nt('designer.setting.addEventHandler') }}</el-button>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n"
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

export default {
  name: "allowCreateFn-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    eventParams() {
      return ['value']
    }
  },
  created() {
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onChange === '' && this.selectedWidget.type == 'transfer') {
          this.$set(
            this.optionModel,
            'onChange',
            `// value 当前key数据  selectedField 已选中的数据集 unSelectedField 未选中的数据集`,
          );
        }
      },
      deep: true,
    },
  },
}
</script>

<style scoped></style>
