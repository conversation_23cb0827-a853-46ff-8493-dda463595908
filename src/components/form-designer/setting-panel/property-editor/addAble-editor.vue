<template>
  <!--  :label="i18nt('designer.setting.required')" -->
  <el-form-item>
    <span slot="label">
      {{ i18nt('designer.setting.addAble') }}
      <el-tooltip v-if="['sub-form','trends-tab'].includes(selectedWidget.type)" effect="light" content="子表单和动态表单是否可以新增">
        <i class="el-icon-info" />
      </el-tooltip>
    </span>
    <el-switch v-model="optionModel.addAble" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'

export default {
  name: 'AddAbleEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped>

</style>
