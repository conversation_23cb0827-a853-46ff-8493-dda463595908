<template>
  <div>
    <el-form-item label="开启权限">
      <el-switch
        v-model="optionModel.authority"
        :min="1"
        class="hide-spin-button"
        style="width: 100%"
      ></el-switch>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
export default {
  name: 'authority-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('authority' in val)) {
          this.$set(this.optionModel, 'authority', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
