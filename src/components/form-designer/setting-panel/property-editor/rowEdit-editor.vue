<template>
  <!--  :label="i18nt('designer.setting.required')" -->
  <el-form-item>
    <span slot="label">
      {{ i18nt('designer.setting.rowEdit') }}
      <el-tooltip v-if="['sub-form','trends-tab'].includes(selectedWidget.type)" effect="light" content="子表单和动态表单单行是否可编辑">
        <i class="el-icon-info" />
      </el-tooltip>
    </span>
    <el-switch v-model="optionModel.rowEdit" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'

export default {
  name: 'rowEditEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  created() {

  }
}
</script>

<style scoped>

</style>
