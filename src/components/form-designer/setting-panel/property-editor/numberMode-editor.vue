<template>
  <div>
    <el-form-item label="数字模式">
      <el-switch v-model="optionModel.numberMode.open" />
    </el-form-item>
    <template v-if="optionModel.numberMode.open">
      <el-form-item label="保留位数">
        <el-input-number
          size="mini"
          v-model="optionModel.numberMode.digit"
          :min="0"
        />
      </el-form-item>
      <el-form-item label="四舍五入">
        <el-switch v-model="optionModel.numberMode.round" />
      </el-form-item>
    </template>
  </div>
</template>

<script>
export default {
  name: 'numberMode-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('numberMode' in val)) {
          this.$set(this.optionModel, 'numberMode', {
            open: false,
            digit: 0,
            round: false,
          });
        }
      },
      deep: true,
    },
  },
};
</script>
