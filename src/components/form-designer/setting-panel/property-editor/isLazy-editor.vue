<template>
  <el-form-item >
    <template slot="label">
      懒加载
       <el-tooltip
        effect="light"
        v-if="optionModel.hasOwnProperty('cols')"
        content="在需要懒加载的行数据内加入'hasChildren'字段"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-switch type="text" v-model="optionModel.isLazy"></el-switch>
  </el-form-item>
</template>

<script>

export default {
  name: 'isLazy-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>

</style>

