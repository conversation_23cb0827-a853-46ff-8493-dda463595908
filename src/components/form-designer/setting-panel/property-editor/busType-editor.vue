<template>
  <el-form-item :label="i18nt('designer.setting.busType')" v-if="!noLabelSetting">
    <el-select v-model="optionModel.busType" placeholder="请选择">
      <el-option v-for="item in busType" :key="item.numberingId" :label="item.businessName" :value="item.numberingId">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import request from '@/utils/request'
  export default {
    name: "busType-editor",
    mixins: [i18n],
    data() {
    return {
      busType:[]
    }
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    mounted:function(){
      this.getBusType();
    },
    methods:{
      getBusType(){
        return request({
          url: '/system/numbering/list',
          method: 'get',
          params: {pageSize:500, pageNum: 1}
        }).then(res=>{
          this.busType=res.data.records
        })
      }
    },
    computed: {
      noLabelSetting() {
        return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
        //|| (this.selectedWidget.type === 'divider')
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
