<template>
  <div>
        <el-form-item label="背景颜色:">
          <el-color-picker
            size="small"
            show-alpha
            :predefine="predefineColors"
            v-model="optionModel.bgColor"
          ></el-color-picker>
        </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
export default {
  name: "bgColor-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
       predefineColors: [
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
        ]
    };
  },
  methods: {},
};
</script>

