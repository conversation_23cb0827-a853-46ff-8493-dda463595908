<template>
  <div>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">时间线设置</el-divider>
    </el-form-item>
    <el-form-item label="当前时间线:"></el-form-item>
    <el-form-item label-width="0">
      <li
        v-for="(activity, index) in optionModel.activities"
        :key="index"
        class="col-item"
      >
        <span>第{{ index + 1 }}项</span>

        <el-form label-position="left" label-width="45px" inline>
          <el-form-item label="时间戳:" >
            <el-input
              size="mini"
              style="width: 100px"
              v-model="activity.timestamp"
            ></el-input>
          </el-form-item>
          <el-form-item label="颜色:">
            <el-color-picker
              size="mini"
              v-model="activity.color"
            ></el-color-picker>
          </el-form-item>
          <el-form-item label="内容:">
            <el-input
              size="mini"
              style="width: 180px"
              v-model="activity.content"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="0">
            <el-button
              circle
              plain
              size="mini"
              type="danger"
              @click="deleteCol(index)"
              icon="el-icon-minus"
            ></el-button>
          </el-form-item>
        </el-form>
      </li>
      <div>
        <el-button type="text" @click="addNewCol">添加时间戳</el-button>
      </div>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";

export default {
  name: "activities-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    deleteCol(index) {
      this.optionModel.activities.splice(index, 1);
    },

    addNewCol() {
      this.optionModel.activities.push({
        timestamp: "",
        color: "",
        content: "",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;

  margin: 5px 0;
}

::v-deep .el-button--mini.is-circle {
  padding: 5px;
}
</style>
