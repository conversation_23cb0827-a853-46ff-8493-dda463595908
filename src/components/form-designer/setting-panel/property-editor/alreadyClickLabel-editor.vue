<template>
  <el-form-item label="存值后标题" >
    <el-input type="text" v-model="optionModel.alreadyClickLable"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "alreadyClickLable-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    watch: {
      optionModel: {
        immediate: true,
        handler(val) {
          // 如果没有这个字段则添加这个字段
          if (!('alreadyClickLable' in val)) {
            // alert('设置为true');
            this.$set(this.optionModel, 'alreadyClickLable', "");
          }
        },
        deep: true,
      },
    },
    computed: {
      noLabelSetting() {
        return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
        //|| (this.selectedWidget.type === 'divider')
      },

    }
  }
</script>

<style lang="scss" scoped>

</style>
