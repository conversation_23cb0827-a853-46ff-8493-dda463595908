<template>
  <el-form-item v-if="!optionModel.treeTable">
    <template #label>
      拖拽排序
      <el-tooltip
        effect="light"
        content="需要设置主键"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-switch v-model="optionModel.isEnable" @change="onMultipleSelected"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "isEnable-editor",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    watch:{
      optionModel: {
        immediate: true,
        handler(val) {
          // 如果没有这个字段则添加这个字段
          if (val.isEnable == null) {
            this.$set(this.optionModel, 'isEnable', false)
          }
        },
        deep: true
      }
    },
  }
</script>

<style scoped>

</style>
