<template>
  <div>
    <el-form-item label="显示全选">
      <el-switch
        v-model="optionModel.showCheckAll"
        :min="1"
        class="hide-spin-button"
        style="width: 100%"
      ></el-switch>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
export default {
  name: 'showCheckAll-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('showCheckAll' in val)) {
          this.$set(this.optionModel, 'showCheckAll', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
