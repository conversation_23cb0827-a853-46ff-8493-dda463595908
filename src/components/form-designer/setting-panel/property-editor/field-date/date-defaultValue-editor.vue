<template>
  <el-form-item label="默认当天">
    <el-switch v-model="optionModel.defaultCurrent"></el-switch>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'date-defaultValue-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('defaultCurrent' in val)) {
          this.$set(this.optionModel, 'defaultCurrent', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
