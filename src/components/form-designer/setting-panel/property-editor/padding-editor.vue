<template>
  <div>
    <el-form-item label="内边距:"></el-form-item>
    <el-form-item label-width="0">
      <el-form label-position="left" inline>
        <el-form-item label="上:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.padding.pt"
          ></el-input>
        </el-form-item>
        <el-form-item label="下:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.padding.pb"
          ></el-input>
        </el-form-item>
        <el-form-item label="左:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.padding.pl"
          ></el-input>
        </el-form-item>
        <el-form-item label="右:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.padding.pr"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
export default {
  name: "padding-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;
  margin: 5px 0;
}
</style>
