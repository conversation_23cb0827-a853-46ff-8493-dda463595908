<template>
  <el-form-item label="是否显示导出">
    <el-switch v-model="optionModel.hasExport" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'hasExport-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('hasExport' in val)) {
          this.$set(this.optionModel, 'hasExport', true);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
