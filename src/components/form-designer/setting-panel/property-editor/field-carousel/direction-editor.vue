<template>
  <el-form-item label="方向">
    <el-radio-group v-model="optionModel.direction" class="radio-group-custom">
      <el-radio-button label="horizontal">
       水平</el-radio-button>
      <el-radio-button label="vertical">
        垂直</el-radio-button>
    </el-radio-group>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "direction-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style lang="scss" scoped>
  .radio-group-custom {
    ::v-deep .el-radio-button__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
</style>
