<template>
  <el-form-item :label="i18nt('designer.setting.groupPage')">
    <el-switch v-model="optionModel.groupPage"  @change="change"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "groupPage-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    methods:{
      change(e){
        // samePage 和 groupPage只能选择一个
        if(this.optionModel.samePage) {
          this.optionModel.samePage= false
        }
      }
    }
  }
</script>

<style scoped>

</style>
