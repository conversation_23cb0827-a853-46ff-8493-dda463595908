<template>
  <el-form-item label="选择条码">
    <el-select v-model="optionModel.codeId" clearable  filterable>
      <el-option v-for="(item, index) in options"
                 :key="index" :label="item.codeName"
                 :value="item.codeId"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import { getCodeManage } from "@/api/interfaces/interfaces";
  export default {
    name: "codeManage-editor",
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    data(){
      return {
        options: []
      }
    },
    methods:{

    },
    mounted() {
      if(this.options.length==0){
        getCodeManage({
          pageSize:999,
          pageNum: 1,
        }).then(res=>{
          this.options=res.data.records
        })
      }
    },

  }
</script>

<style lang="scss" scoped>

</style>
