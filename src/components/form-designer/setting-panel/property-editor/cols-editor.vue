<template>
  <div>
    <el-form-item label="表格列配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogShow = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      v-dialog-drag
      title="表格列配置"
      width="95%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table
        v-model="optionModel.cols"
        :isEnable="isEnable"
        max-height="500"
        row-key="id"
        default-expand-all
      >
        <lt-table-column-edit prop="label" label="列名" show-overflow-tooltip />
        <lt-table-column-edit prop="prop" label="字段" show-overflow-tooltip />
        <lt-table-column-edit prop="width" label="宽度" />
        <lt-table-column-edit
          prop="align"
          label="对齐方式"
          state="select"
          :select-opt="{ options: $enum.alignFix }"
        />
        <lt-table-column-edit label="缩略显示" state="switch" prop="overflow" />

        <lt-table-column-operation
          width="300"
          v-model="optionModel.cols"
          :row-data="rowObj"
          :unshift="false"
          fixed="right"
          ref="operationRef"
          @save="save"
          @delete="del"
          @addBefore="addBefore"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="prop"
          :layout="['delete', 'edit', 'save']"
        >
          <template #after="{ row }">
            <el-link
              :underline="false"
              type="primary"
              style="margin-left: 5px"
              @click="showEdit(row)"
            >
              其他配置
            </el-link>
            <el-link
              :underline="false"
              type="primary"
              style="margin-left: 5px"
              v-if="!row.isEdit"
              @click="addChildren(row)"
            >
              添加子集
            </el-link>
            <template v-if="!isEnable">
              <el-link
                :underline="false"
                type="success"
                style="margin-left: 5px"
                v-if="!row.isEdit && isShowMove(row, 'top')"
                @click="handleMove(row, 'top')"
              >
                上移
              </el-link>
              <el-link
                :underline="false"
                type="success"
                style="margin-left: 5px"
                v-if="!row.isEdit && isShowMove(row, 'bottom')"
                @click="handleMove(row, 'bottom')"
              >
                下移
              </el-link>
            </template>
          </template>
        </lt-table-column-operation>
      </lt-sort-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-dialog-drag
      title="列点击事件"
      :visible="clickDialog"
      :show-close="true"
      width="80%"
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      @close="clickDialog = false"
    >
      <div class="d-flex" v-if="clickShow">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <el-alert
            type="info"
            :closable="false"
            title="columnClick(row,$index){"
          />
          <code-editor
            v-model="clickCode"
            :mode="'javascript'"
            :readonly="false"
          />
          <el-alert type="info" :closable="false" title="}" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clickDialog = false"> 取消</el-button>
        <el-button type="primary" @click="saveClick"> 确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialog-drag
      title="自定义HTML"
      :visible="htmlDialog"
      show-close
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="htmlDialog = false"
    >
      <div class="d-flex" v-if="custHtmlFlag">
        <div class="flex-1">
          <el-alert
            type="info"
            :closable="false"
            title="customHtml(row,$index,text){"
          />
          <code-editor
            v-model="codeSave"
            :mode="'javascript'"
            :readonly="false"
          />
          <el-alert type="info" :closable="false" title="}" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="htmlDialog = false"> 取消</el-button>
        <el-button type="primary" @click="saveHtml()"> 确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialog-drag
      title="select配置"
      :visible="selectDialog"
      show-close
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="selectDialog = false"
    >
      <ApiEditor :optionModel="row.selectConfig" />
      <SelectLabel :optionModel="row.selectConfig" />
      <SelectValue :optionModel="row.selectConfig" />
      <OptionItemsDditor
        :selectedWidget="{ ...row.selectConfig, showDefault: false }"
      />
      <el-alert title="数据来源的优先级会比自定义选项高" type="warning" />
      <AllowCreateEditor :optionModel="row.selectConfig" />

      <div slot="footer" class="dialog-footer">
        <el-button @click="selectDialog = false"> 取消</el-button>
        <el-button type="primary" @click="saveSelect">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialog-drag
      width="60%"
      :title="row.label + '列配置'"
      :visible="editVisible"
      class="small-padding-dialog"
      :close-on-click-modal="false"
      @close="editVisible = false"
    >
      <el-form ref="form" :model="row" label-width="70px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="列固定">
              <el-select v-model="row.fix">
                <el-option label="左" value="left"></el-option>
                <el-option label="右" value="right"></el-option>
                <el-option label="无" value=""></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合计">
              <el-switch v-model="row.total" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="图标选择">
          <el-popover
            placement="bottom-start"
            width="460"
            trigger="click"
            @show="$refs['iconSelect'].reset()"
          >
            <IconSelect ref="iconSelect" @selected="selected($event, row)" />
            <el-input
              slot="reference"
              v-model="row.icon"
              placeholder="点击选择图标"
              readonly
            >
              <svg-icon
                v-if="row.icon"
                slot="prefix"
                :icon-class="row.icon"
                class="el-input__icon"
                style="height: 32px; width: 16px"
              />
              <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
          </el-popover>
        </el-form-item>

        <el-form-item label="显示类型">
          <el-radio-group v-model="row.other" @input="radioChange">
            <el-radio label="hnbj">行内编辑</el-radio>
            <el-radio label="nzlx">内置类型</el-radio>
            <el-radio label="html">自定义html</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="row.other == 'hnbj'">
          <el-form-item label="可编辑">
            <el-switch v-model="row.editable" />
          </el-form-item>
          <el-form-item label="编辑类型">
            <el-select v-model="row.editType">
              <el-option label="输入框" value="input"></el-option>
              <el-option label="日期" value="date"></el-option>
              <el-option label="日期时间" value="datetime"></el-option>
              <el-option label="数字" value="number"></el-option>
              <el-option label="下拉框" value="select"></el-option>
              <el-option label="开关" value="switch"></el-option>
              <el-option label="自定义" value="custom"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="row.other == 'hnbj' && row.editType == 'select'"
            label="下拉框配置"
          >
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              size="mini"
              round
              @click="editSelect(row)"
              >配置</el-button
            >
          </el-form-item>
        </template>
        <template v-else-if="row.other == 'nzlx'">
          <el-form-item label="内置类型">
            <el-select v-model="row.colType">
              <el-option label="用户名称" value="user"></el-option>
              <el-option label="开关" value="switch"></el-option>
              <el-option label="链接" value="link"></el-option>
              <el-option label="标签" value="tag"></el-option>
              <el-option label="时间" value="time"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="链接事件">
            <el-tooltip
              effect="light"
              content="仅用于内置类型为链接或开关时，其他类型请使用表格单击单元格事件"
            >
              <el-button
                type="primary"
                icon="el-icon-edit"
                size="mini"
                plain
                round
                @click="editClickCode(row)"
                >编写代码</el-button
              >
            </el-tooltip>
          </el-form-item>
        </template>
        <el-form-item v-else-if="row.other == 'html'" label="自定义html">
          <el-button
            type="primary"
            icon="el-icon-edit"
            plain
            size="mini"
            round
            @click="editHtml(row)"
            >编写代码</el-button
          >
        </el-form-item>
        <el-form-item
          v-if="row.other == 'nzlx' && row.colType == 'tag'"
          label="标签类型"
        >
          <el-button type="primary" @click="addTagType(row)"
            ><i class="el-icon-plus"></i>添加</el-button
          >
          <el-form
            :inline="true"
            class="demo-form-inline mr-10"
            v-for="(item, index) in row.tagTypes"
            :key="index"
          >
            <el-form-item label="值">
              <el-input
                v-model="item.value"
                placeholder="输入字段值"
              ></el-input>
            </el-form-item>
            <el-form-item label="标签显示标题">
              <el-input
                v-model="item.label"
                placeholder="标签显示标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="显示类型">
              <el-select v-model="item.type" placeholder="显示类型">
                <el-option label="默认" value="default"></el-option>
                <el-option label="主要" value="primary"></el-option>
                <el-option label="成功" value="success"></el-option>
                <el-option label="信息" value="info"></el-option>
                <el-option label="警告" value="warning"></el-option>
                <el-option label="危险" value="danger"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="danger"
                size="mini"
                plain
                round
                @click="row.tagTypes.splice(index, 1)"
                >删除</el-button
              >
            </el-form-item>
          </el-form>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false"> 取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import CodeEditor from '@/components/code-editor/index.vue';
import IconSelect from '@/components/IconSelect';
import ApiEditor from '@/components/form-designer/setting-panel/property-editor/api-editor.vue';
import SelectLabel from '@/components/form-designer/setting-panel/property-editor/selectLabel-editor.vue';
import SelectValue from '@/components/form-designer/setting-panel/property-editor/selectValue-editor.vue';
import OptionItemsDditor from '@/components/form-designer/setting-panel/property-editor/optionItems-editor.vue';
import AllowCreateEditor from '@/components/form-designer/setting-panel/property-editor/allowCreate-editor.vue';
import componentTree from '../components/componentTree.vue';
const selectConfig = {
  api: '',
  onSuccessCallback: '',
  onFailCallback: '',
  selectLabel: 'label',
  selectValue: 'value',
  options: {
    optionItems: [
      {
        label: 'select 1',
        value: '1',
      },
      {
        label: 'select 2',
        value: '2',
      },
      {
        label: 'select 3',
        value: '3',
      },
    ],
  },
  type: 'select',
  allowCreate: false,
};
export default {
  name: 'cols-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    CodeEditor,
    IconSelect,
    ApiEditor,
    SelectLabel,
    SelectValue,
    OptionItemsDditor,
    AllowCreateEditor,
    componentTree,
  },
  data() {
    return {
      dialogShow: false,
      row: {},
      custHtmlFlag: true,
      clickShow: true,
      codeSave: '',
      clickCode: '',
      htmlDialog: false,
      clickDialog: false,
      rowObj: {
        label: '',
        prop: '',
        fix: '',
        align: '',
        width: '',
        editable: true,
        editType: 'input',
        icon: '',
        code: ``,
        sortable: false,
        overflow: true,
        link: false,
        tag: false,
        total: 0,
        hidden: false,
        isEdit: true,
        other: '',
        selectConfig: selectConfig,
      },
      selectDialog: false,
      editVisible: false,
      currentObj: {},
      oldOther: '',
    };
  },
  computed: {
    isEnable() {
      return this.optionModel.cols.every(
        (item) => !item.children || item.children.length == 0,
      );
    },
  },
  mounted() {
    this.optionModel.cols.forEach((item, index) => {
      if (!item.id) {
        this.$set(item, 'id', +new Date().getTime());
      }
      if (!item.align) {
        this.$set(item, 'align', 'left');
      }

      if (!item.hasOwnProperty('editable')) {
        this.$set(item, 'editable', true);
      }
      if (!item.hasOwnProperty('editType')) {
        this.$set(item, 'editType', 'input');
      }
    });
  },

  methods: {
    addTagType(row) {
      if (!row.tagTypes) {
        this.$set(row, 'tagTypes', [{ value: '', label: '', type: '' }]);
      } else {
        row.tagTypes.push({ value: '', label: '', type: '' });
      }
    },
    showEdit(row) {
      this.editVisible = true;
      this.currentObj = row;
      this.row = JSON.parse(JSON.stringify(row));
    },
    confirm() {
      for (let i in this.row) {
        this.currentObj[i] = this.row[i];
      }
      this.editVisible = false;
    },
    selected(e, row) {
      row.icon = e;
    },
    saveHtml() {
      this.row.code = this.codeSave;
      this.htmlDialog = false;
    },
    saveClick() {
      this.row.clickCode = this.clickCode;
      this.clickDialog = false;
    },
    editHtml(row) {
      this.custHtmlFlag = false;
      this.codeSave = row.code;
      this.$nextTick(() => {
        this.custHtmlFlag = true;
      });
      this.htmlDialog = true;
    },
    editClickCode(row) {
      this.clickShow = false;
      this.clickCode = row.clickCode || '';
      this.$nextTick(() => {
        this.clickShow = true;
        this.$nextTick(() => {
          this.$refs.componentTreeRef.init(this.designer.widgetList);
        });
      });
      this.clickDialog = true;
    },
    addChildren(row) {
      this.$refs.operationRef.addChildren(row);
    },
    addBefore() {
      this.rowObj.id = +new Date().getTime();
    },
    save({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    del({ data, index }) {
      function getItem(arr, id) {
        arr.forEach((item, index2) => {
          if (item.id && item.id === id) {
            arr.splice(index2, 1);
          } else {
            item.children && getItem(item.children, id);
          }
        });
      }
      getItem(this.optionModel.cols, data.id);
      this.$forceUpdate();
    },
    editSelect(row) {
      if (!row.editable) {
        this.$message('请启用可编辑');
        return;
      }
      if (!('selectConfig' in this.row)) {
        this.$set(this.row, 'selectConfig', { ...selectConfig });
      }
      // 字段比较,判断是否有某个字段,没有则追加
      for (let key in selectConfig) {
        if (!(key in this.row.selectConfig)) {
          this.$set(this.row.selectConfig, key, selectConfig[key]);
        }
      }
      this.selectDialog = true;
    },
    saveSelect() {
      this.selectDialog = false;
    },
    isShowMove(row, moveFlag) {
      const { id } = row;
      if (id) {
        let isShow = null;
        // 找到id对应的节点
        const dfs = (root) => {
          let index = root.findIndex((item) => item.id === id);
          if (index !== -1) {
            if (moveFlag === 'top') {
              isShow = index !== 0;
            } else {
              isShow = index !== root.length - 1;
            }
          } else {
            root.forEach((item) => {
              item.children && dfs(item.children);
            });
          }
        };
        dfs(this.optionModel.cols);

        return isShow;
      }
      return false;
    },
    handleMove(row, moveFlag) {
      let dataSource = {
        id: 99999999,
        children: JSON.parse(JSON.stringify(this.optionModel.cols)),
      };
      let isComplete = false;
      const dfs = (root) => {
        if (!isComplete) {
          let i =
            root.children &&
            root.children.findIndex((item) => item.id === row.id);
          if (i > -1) {
            let positionalIndex = moveFlag === 'top' ? i - 1 : i + 1;

            let temp = root.children[i];
            root.children[i] = root.children[positionalIndex];
            root.children[positionalIndex] = temp;
            isComplete = true;
          }
          root.children && root.children.forEach((item) => dfs(item));
        }
      };

      dfs(dataSource);

      this.optionModel.cols = dataSource.children;
    },
    radioChange(val) {
      if (val && this.oldOther) {
        const checkData = [
          { key: 'hnbj', label: '行内编辑' },
          { key: 'nzlx', label: '内置类型', value: this.row.colType },
          { key: 'html', label: '自定义html', value: this.row.code },
        ];

        if (
          ['nzlx', 'html'].includes(this.oldOther) &&
          (this.row.colType || this.row.code)
        ) {
          let start = checkData.find(
            (item) => item.key === this.oldOther,
          ).label;
          let end = checkData.find((item) => item.key === val).label;

          this.$confirm(
            `当前操作正在从'${start}'切换至'${end}', 将会清空关于'${start}'的配置, 是否继续?`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          )
            .then(() => {
              if (this.oldOther === 'nzlx') this.row.colType = undefined;
              if (this.oldOther === 'html') this.row.code = null;
              this.oldOther = val;
            })
            .catch(() => {
              this.row.other = this.oldOther;
            });
        } else {
          this.oldOther = val;
        }
      } else {
        this.oldOther = val;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;

  margin: 5px 0;
}

::v-deep .cell {
  display: flex;
}

::v-deep .el-button--mini.is-circle {
  padding: 5px;
}

::v-deep .el-radio-button--mini .el-radio-button__inner {
  padding: 7px 10px;
}
</style>
