<template>
<div>
  <el-form-item label="显示快捷回复">
    <el-switch v-model="optionModel.showReply"></el-switch>
  </el-form-item>
  <el-form-item label="默认回复">
      <el-tag class="mr-3" v-for="item in optionModel.defaultReply" :key="item" type="info" closable @close="handleClose(item)">{{item.length>8?item.slice(0,8)+'...':item}}</el-tag>
      <el-input v-model="str" placeholder="添加回复" class="mr-t10">
        <el-button slot="append" @click="add" icon="el-icon-circle-plus-outline"></el-button>
      </el-input>
  </el-form-item>
  <el-form-item label="允许回复自定义">
    <el-switch v-model="optionModel.custom"></el-switch>
  </el-form-item>

</div>
  
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "showReply-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        str:''
      }
    },
    methods:{
      add(){
        // 添加tag
        if(!this.str){
          return
        } 
        this.optionModel.defaultReply.push(this.str)
        this.str=''
      },
      handleClose(tag){
        // 删除tag
        this.optionModel.defaultReply.splice(this.optionModel.defaultReply.indexOf(tag), 1)
      }
    }
  }
</script>

<style scoped>

</style>
