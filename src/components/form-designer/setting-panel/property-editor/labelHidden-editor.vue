<template>
  <el-form-item :label="i18nt('designer.setting.labelHidden')">
    <el-switch v-model="optionModel.labelHidden"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "labelHidden-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    watch: {
      optionModel: {
        immediate: true,
        handler(val) {
          // 如果没有这个字段则添加这个字段
          if (val.labelHidden == null) {
            this.$set(this.optionModel, 'labelHidden', true);
          }
        },
        deep: true,
      },
    },
  }
</script>

<style scoped>

</style>
