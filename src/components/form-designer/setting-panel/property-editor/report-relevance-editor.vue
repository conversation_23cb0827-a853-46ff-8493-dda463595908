<template>
  <el-form-item label="报表关联接口">
    <el-select
      v-model="optionModel.reportRelevance"
      placeholder="请选择报表关联接口"
    >
      <el-option
        v-for="item in reportData"
        :key="item.reportCode"
        :label="item.reportName"
        :value="item.reportCode"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import { reportList } from '@/api/bigscreen';

export default {
  name: 'report-relevance-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      reportData: [],
    };
  },
  created() {
    reportList({
      pageNum: 1,
      pageSize: 99999,
      reportType: 'report_report_excel',
    }).then((res) => {
      this.reportData = res.data.records;
    });
  },
};
</script>

<style lang="scss" scoped></style>
