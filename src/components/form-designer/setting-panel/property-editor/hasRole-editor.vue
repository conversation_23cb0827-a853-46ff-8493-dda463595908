<template>
  <el-form-item label="是否校验">
    <el-switch v-model="optionModel.hasRole" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'hasRole-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('hasRole' in val)) {
          // alert('设置为true');
          this.$set(this.optionModel, 'hasRole', true);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
