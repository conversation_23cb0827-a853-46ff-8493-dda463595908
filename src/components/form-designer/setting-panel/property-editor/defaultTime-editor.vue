<template>
  <el-form-item :label="i18nt('designer.setting.defaultTime')">
    <el-switch v-model="optionModel.defaultTime"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "defaultTime-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
