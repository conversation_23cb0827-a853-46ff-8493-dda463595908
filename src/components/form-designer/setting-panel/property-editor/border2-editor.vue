<template>
  <div>
    <el-form-item label="边框:"></el-form-item>
    <el-form-item label-width="0">
      <el-form label-position="left" inline>
        <el-form-item label="宽度:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.border2.width"
          ></el-input>
        </el-form-item>
        <el-form-item label="颜色:">
          <el-color-picker
            size="small"
            v-model="optionModel.border2.color"
            show-alpha
                     :predefine="predefineColors"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="类型:">
          <el-select
            size="mini"
            style="width: 150px"
            v-model="optionModel.border2.type"
          >
          <el-option label="实线" value="solid"></el-option>
          <el-option label="虚线" value="dotted"></el-option>
          </el-select>
        </el-form-item>
        
      </el-form>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
export default {
  name: "border2-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      predefineColors: [
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
        ]
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;
  margin: 5px 0;
}
</style>
