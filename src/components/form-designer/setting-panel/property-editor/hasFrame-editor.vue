<template>
  <el-form-item label="边框线">
    <el-switch v-model="optionModel.hasFrame" :min="1" class="hide-spin-button" style="width: 100%"></el-switch>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
import CodeEditor from '@/components/code-editor/index.vue'
export default {
  name: 'hasFrame-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      dialogFlag: false
    }
  }
}
</script>

<style scoped>

</style>
