<template>
  <el-form-item label="自动加载数据">
    <el-switch v-model="optionModel.autoLoadData" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';

export default {
  name: 'autoLoadData-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        if ('authLoadData' in val && !('autoLoadData' in val))
          this.$set(this.optionModel, 'autoLoadData', val.authLoadData);
        // 如果没有这个字段则添加这个字段
        if (!('autoLoadData' in val)) {
          this.$set(this.optionModel, 'autoLoadData', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
