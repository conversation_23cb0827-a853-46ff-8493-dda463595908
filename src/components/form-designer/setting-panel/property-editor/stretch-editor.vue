<template>
  <el-form-item label="标签宽度自撑开">
    <el-switch
      v-model="optionModel.stretch"
      :min="1"
      class="hide-spin-button"
      style="width: 100%"
    />
  </el-form-item>
</template>

<script>
export default {
  name: 'stretch-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('stretch' in val)) {
          this.$set(this.optionModel, 'stretch', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
