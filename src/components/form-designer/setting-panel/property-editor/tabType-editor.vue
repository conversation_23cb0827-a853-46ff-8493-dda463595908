<template>
  <el-form-item label="风格类型">
    <el-select v-model="optionModel.tabType" filterable>
      <el-option
        v-for="(item, idx) in typeList"
        :key="idx"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
export default {
  name: 'tabType-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      typeList: [
        {
          label: '卡片化',
          value: 'border-card',
        },
        {
          label: '选项卡',
          value: 'card',
        },
        {
          label: '默认',
          value: '',
        },
      ],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('tabType' in val)) {
          this.$set(this.optionModel, 'tabType', 'border-card');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
