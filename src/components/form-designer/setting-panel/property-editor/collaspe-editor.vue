<template>
  <div>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">折叠面板设置</el-divider>
    </el-form-item>
    <el-form-item label="当前项"></el-form-item>
    <el-form-item label-width="0">
      <li v-for="(colItem, colIdx) in selectedWidget.items" :key="colIdx" class="col-item">
        <span class="col-span-title">第{{colIdx + 1}}项</span>
        <div class="d-flex j-sb">
          <span>标题:</span>
           <el-input type="text" v-model="colItem.options.title" 
                         style="width:80%"></el-input>
        </div>
       
        <el-button circle plain size="mini" type="danger" @click="deleteCol(selectedWidget, colIdx)"
                   icon="el-icon-minus" class="col-delete-button"></el-button>
      </li>
      <div>
        <el-button type="text" @click="addNewCol(selectedWidget)">添加</el-button>
      </div>
    </el-form-item>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "collapse-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    methods: {

      deleteCol(curGrid, colIdx) {
        // this.designer.deleteColOfGrid(curGrid, colIdx)
        curGrid.items.splice(colIdx,1)
        this.designer.emitHistoryChange()
      },

      addNewCol(curGrid) {
        this.designer.addNewCollapseItem(curGrid)
        this.designer.emitHistoryChange()
      },

    }
  }
</script>

<style lang="scss" scoped>
  li.col-item {
    list-style: none;

    span.col-span-title {
      display: inline-block;
      font-size: 13px;
      width: 80px;
    }

    .col-delete-button {
      margin-left: 6px;
    }
  }

</style>
