<template>
  <div>
   <el-form-item label="自定义">
              <el-switch v-model="optionModel.customHtml">自定义html</el-switch>
    </el-form-item>

    <el-form-item label="编写" v-if="optionModel.customHtml">
              <el-button size="mini" type="primary" @click="showDialog">编写代码</el-button>
    </el-form-item>
    <el-dialog title="编写html" width="80%" :visible="dialogShow" :show-close="false">
      <div class="d-flex">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <el-alert type="info" :closable="false" title="customHtml(date,data(type, isSelected, day)){" />
          <code-editor
            v-model="fun"
            :mode="'javascript'"
            :readonly="false"
          />
          <el-alert type="info" :closable="false" title="}" />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false">
          取消</el-button>
        <el-button type="primary" @click="confirm">
          确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"
import CodeEditor from "@/components/code-editor/index.vue";
import componentTree from '../components/componentTree.vue';
  export default {
    name: "slot-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
     components: {
    CodeEditor,
    componentTree
  },
    data(){
      return{
        dialogShow:false,
        fun:'',
      }
    },
    methods: {
      showDialog(){
        this.fun=this.optionModel.slot
        this.dialogShow=true
        this.$nextTick(() => {
          this.$refs.componentTreeRef.init(this.designer.widgetList);
        });
      },
      confirm(){
        if(this.fun){
          this.optionModel.slot=this.fun
        }
        this.dialogShow=false
      }

    }
  }
</script>

<style lang="scss" scoped>
  li.col-item {
    list-style: none;

    margin: 5px 0;
  }

  ::v-deep .el-button--mini.is-circle{
    padding: 5px;
  }

</style>
