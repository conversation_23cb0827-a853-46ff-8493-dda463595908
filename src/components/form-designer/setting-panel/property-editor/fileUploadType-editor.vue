<template>
  <div>
    <el-form-item label="上传样式类型">
      <el-select v-model="optionModel.fileUploadType" placeholder="请选择">
        <el-option v-for="item in type" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="自定义按钮文字">
      <el-input v-model="optionModel.fileUploadButtonLabel"/>
    </el-form-item>
  </div>

</template>

<script>
  import i18n from "@/utils/i18n"
  import request from '@/utils/request'
  export default {
    name: "fileUploadType-editor",
    mixins: [i18n],
    data() {
    return {
      type:[
        {label: '按钮', value: 'button'},
        {label: '拖拽', value: 'drag'},
      ]
    }
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    mounted:function(){
      this.getBusType();
    },
    methods:{
      getBusType(){
        return request({
          url: '/flows/processModel/busByFunctional',
          method: 'get',
          params: {busId:1}
        }).then(res=>{
          this.busType=res.data
        })
      }
    },
    computed: {
      noLabelSetting() {
        return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
        //|| (this.selectedWidget.type === 'divider')
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
