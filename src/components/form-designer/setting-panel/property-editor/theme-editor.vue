<template>
  <el-form-item label="主题">
    <el-select v-model="optionModel.theme">
      <el-option v-for="item in themeList" :key="item" :label="item"
                 :value="item">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "theme-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        themeList:require.context('echarts/theme', false, /\.js$/).keys().map(item=>item.slice(2,-3)),
      }
    }
  }
</script>

<style scoped>

</style>
