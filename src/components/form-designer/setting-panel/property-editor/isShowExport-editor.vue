<template>
  <el-form-item :label="i18nt('designer.setting.isShowExport')">
    <el-switch
      v-model="optionModel.isShowExport"
      @change="onMultipleSelected"
    />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'isShowExport-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.isShowExport == null) {
          this.$set(this.optionModel, 'isShowExport', true);
        }
      },
      deep: true,
    },
  },
};
</script>
