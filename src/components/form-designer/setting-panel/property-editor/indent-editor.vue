<template>
  <div>
    <el-form-item
      label="水平缩进"
    >
      <el-input-number
        v-model="optionModel.indent">
      </el-input-number>
    </el-form-item>
    <el-form-item
      label="点击节点展开/收缩"
    >
      <el-switch
        v-model="optionModel.expandOnClickNode">
      </el-switch>
    </el-form-item>
    <el-form-item
      label="显示右侧文字"
    >
      <el-switch
        v-model="optionModel.showRightText">
      </el-switch>
    </el-form-item>
    
  </div>

</template>

<script>
import i18n from '@/utils/i18n'
export default {
  name: 'indent-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {}
  },
  watch: {
  },
  created() {},
  methods: {
  },
}
</script>
