<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.tableShow')">
      <el-switch v-model="optionModel.tableShow" />
    </el-form-item>
    <el-form-item v-if="optionModel.tableShow" label="是否排序">
      <el-switch v-model="optionModel.tableOrder" />
    </el-form-item>
    <el-form-item v-if="optionModel.tableShow" label="权重">
      <el-input-number v-model="optionModel.tableShowWeight" />
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@/utils/i18n'

export default {
  name: 'TableShowEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped>

</style>
