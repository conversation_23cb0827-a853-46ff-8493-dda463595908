<template>
  <el-form-item v-if="!optionModel.height && !optionModel.heightFit " label="固定高度">
    <el-input type="number" v-model="optionModel.minHeight"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "minHeight-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
