<template>
  <el-form-item label="位置">
    <el-select v-model="optionModel.tabPosition" filterable>
      <el-option
        v-for="(item, idx) in typeList"
        :key="idx"
        :label="item"
        :value="item"
      ></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
export default {
  name: 'tabPosition-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      typeList: ['top', 'right', 'bottom', 'left'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('tabPosition' in val)) {
          this.$set(this.optionModel, 'tabPosition', 'top');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
