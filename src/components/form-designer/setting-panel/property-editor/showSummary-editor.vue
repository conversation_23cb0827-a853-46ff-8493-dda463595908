<template>
  <el-form-item>
    <template #label>
      显示合计
      <el-tooltip
        effect="light"
        content="还需要在表格列中配置具体是哪一列需要合计"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-switch
      v-model="optionModel.showSummary"
      :min="1"
      class="hide-spin-button"
      style="width: 100%"
    ></el-switch>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
export default {
  name: 'showSummary-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.showSummary == null) {
          this.$set(this.optionModel, 'showSummary', false);
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      dialogFlag: false,
    };
  },
};
</script>

<style scoped></style>
