<template>
  <div>
    <el-form-item label="外边距:"></el-form-item>
    <el-form-item label-width="0">
      <el-form label-position="left" inline>
        <el-form-item label="上:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.margin.mt"
          ></el-input>
        </el-form-item>
        <el-form-item label="下:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.margin.mb"
          ></el-input>
        </el-form-item>
        <el-form-item label="左:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.margin.ml"
          ></el-input>
        </el-form-item>
        <el-form-item label="右:">
          <el-input
            size="mini"
            style="width: 80px"
            type="number"
            v-model="optionModel.margin.mr"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
export default {
  name: 'margin-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        if (!('margin' in this.optionModel)) {
          this.$set(this.optionModel, 'margin', {
            mt: '',
            mb: '',
            ml: '',
            mr: '',
          });
        }
        if (!('padding' in this.optionModel)) {
          this.$set(this.optionModel, 'padding', {
            pt: '',
            pb: '',
            pl: '',
            pr: '',
          });
        }
        if (!('border2' in this.optionModel)) {
          this.$set(this.optionModel, 'border2', {
            width: '',
            type: 'solid',
            color: '',
          });
        }
      },
      deep: true,
    },
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;
  margin: 5px 0;
}
</style>
