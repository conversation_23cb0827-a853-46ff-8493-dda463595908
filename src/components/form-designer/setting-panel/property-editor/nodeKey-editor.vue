<template>
  <el-form-item label="唯一标识">
    <el-input
      type="text"
      v-model="optionModel.nodeKey"
      placeholder="默认为: id"
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'nodeKey-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('nodeKey' in val)) {
          this.$set(this.optionModel, 'nodeKey', 'id');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
