<template>
  <el-form-item>
    <template #label>
      分页样式
      <el-tooltip effect="light" content="分页样式">
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-select v-model="optionModel.pageStyle" multiple>
      <el-option label="total" value="total" />
      <el-option label="sizes" value="sizes" />
      <el-option label="prev" value="prev" />
      <el-option label="pager" value="pager" />
      <el-option label="next" value="next" />
      <el-option label="jumper" value="jumper" />
      <!-- <el-option label="->" value="->" /> -->
      <!-- <el-option label="slot" value="slot" /> -->
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
export default {
  name: 'pageStyle-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.pageStyle || !val.pageStyle.length) {
          this.$set(this.optionModel, 'pageStyle', [
            'total',
            'sizes',
            'prev',
            'pager',
            'next',
            'jumper',
          ]);
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      dialogFlag: false,
    };
  },
};
</script>

<style scoped></style>
