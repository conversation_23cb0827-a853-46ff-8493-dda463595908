<template>
  <el-form-item label="打开方向">
    <el-radio-group v-model="optionModel.drawerDirection">
      <el-radio label="ltr">从左往右开</el-radio>
      <el-radio label="rtl">从右往左开</el-radio>
      <el-radio label="ttb">从上往下开</el-radio>
      <el-radio label="btt">从下往上开</el-radio>
    </el-radio-group>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";

export default {
  name: "drawer-direction-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
  },
};
</script>

<style lang="scss" scoped>

</style>
