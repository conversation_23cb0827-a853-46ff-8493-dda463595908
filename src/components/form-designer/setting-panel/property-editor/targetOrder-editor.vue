<template>
  <el-form-item label="排序方式">
    <el-select v-model="optionModel.targetOrder">
      <el-option 
                 key="original" label="与左侧一致"
                 value="original"></el-option>
                 <el-option 
                 key="push" label="追至末尾"
                 value="push"></el-option>
                 <el-option 
                 key="unshift" label="插入首端"
                 value="unshift"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  export default {
    name: "targetOrder-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    data(){
      return {
        options: []
      }
    },

    mounted() {
    },
    methods:{
    },
  }
</script>

