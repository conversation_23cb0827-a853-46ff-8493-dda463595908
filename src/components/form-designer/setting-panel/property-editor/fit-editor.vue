<template>
<div>
   <el-form-item label="最大显示数">
    <el-input-number v-model="optionModel.maxShow" :min="1" @change="maxChange"  class="hide-spin-button" style="width: 100%"></el-input-number>
  </el-form-item>
   <el-form-item label="只显示下标">
    <el-input-number v-model="optionModel.showIndex" :min="1" @change="indexChange" class="hide-spin-button" style="width: 100%"></el-input-number>
  </el-form-item>
   <el-form-item label="显示方式">
    <el-select v-model="optionModel.fit" >
      <el-option
        v-for="(item, idx) in fitList"
        :key="idx"
        :label="item"
        :value="item"
      ></el-option>
    </el-select>
  </el-form-item>
</div>
 
</template>

<script>
export default {
  name: 'fit-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      fitList: ['fill','contain','cover','none','scale-down'],
    };
  },
  methods:{
    maxChange(e){
      if(e){
        this.optionModel.showIndex=undefined
      }
    },
    indexChange(e){
      if(e){
        this.optionModel.maxShow=undefined
      }
    }
  }
};
</script>

<style scoped></style>
