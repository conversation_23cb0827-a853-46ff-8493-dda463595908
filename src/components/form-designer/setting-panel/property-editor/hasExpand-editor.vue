<template>
  <el-form-item label="开启展开行">
    <el-switch v-model="optionModel.hasExpand" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'hasExpand-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('hasExpand' in val)) {
          // alert('设置为true');
          this.$set(this.optionModel, 'hasExpand', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
