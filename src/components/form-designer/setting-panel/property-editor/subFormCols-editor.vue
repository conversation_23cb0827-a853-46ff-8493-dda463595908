<template>
  <div>
    <el-form-item label="表格列配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogShow = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      title="表格列配置"
      width="60%"
      :visible="dialogShow"
      :show-close="false"
    >
      <el-table :data="selectedWidget.widgetList" max-height="500" row-key="id">
        <el-table-column   label="名称" prop="options.label">
        </el-table-column>
        <el-table-column   label="宽度">
            <template slot-scope="{row}">
                <el-input v-model="row.width"></el-input>
            </template>

        </el-table-column>

 
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>
 
  </div>
</template>

<script>
import i18n from '@/utils/i18n';

export default {
  name: 'subFormCols-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    optionModel: Object,
    selectedWidget:Object
  },
  data() {
    return {
      dialogShow: false,
      row: {},
      rowObj: {
        id:null,
        name:"",
        width: '',
        isEdit:true
      },
    };
  },
  watch:{
    'selectedWidget.widgetList':{
      deep:true,
      handler(n,o){
        n.forEach(item=>{
          if(!Object.keys(item).includes('width')){
            this.$set(item,'width','')
          }
        })
      },
      immediate:true
    }
  },
  mounted() {
    
  },
  methods: {
  
  },
};
</script>

<style lang="scss" scoped>

</style>
