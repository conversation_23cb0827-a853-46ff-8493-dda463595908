<template>
  <el-form-item label="斑马纹">
    <el-switch v-model="optionModel.tableStripe" />
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n"
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

export default {
  name: "stripe-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.tableStripe == null) {
          this.$set(this.optionModel, "tableStripe", false)
        }
      },
      deep: true,
    },
  },
}
</script>
