<template>
    <div>
        <el-form-item label="进度条类型">
            <el-select v-model="optionModel.progressType">
                <el-option v-for="item in typeOption" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="宽度">
            <el-input v-show="optionModel.progressType !== 'line'" type="number" v-model="optionModel.LineWidth"></el-input>
            <el-input v-show="optionModel.progressType == 'line'" type="number"
                v-model="optionModel.strokeWidth"></el-input>
        </el-form-item>
        <el-form-item label="文字嵌入进度条" v-if="optionModel.progressType == 'line'">
            <el-switch v-model="optionModel.progressTextInside" />
        </el-form-item>
        <el-form-item label="状态">
            <el-select v-model="optionModel.progressStatus" placeholder="请选择" clearable @clear="clearStatus">
                <el-option v-for="item in statusOption" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="显示文字内容">
            <el-switch v-model="optionModel.progressShowText" />
        </el-form-item>
        <el-form-item label="进度条颜色">
            <el-color-picker v-model="optionModel.progressColor"></el-color-picker>
        </el-form-item>
        <!-- <el-form-item label="文字颜色">
            <el-color-picker v-model="optionModel.progressTextColor"></el-color-picker>
        </el-form-item>
        <el-form-item label="进度条底色">
            <el-color-picker  v-model="optionModel.progressDefineBackColor"></el-color-picker>
        </el-form-item> -->
    </div>
</template>
    
<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
export default {
    name: 'progress-type',
    mixins: [i18n, propertyMixin],
    props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
    },
    data() {
        return {
            statusOption: [
                {
                    label: '成功',
                    value: 'success'
                },
                {
                    label: '异常',
                    value: 'exception'
                },
                {
                    label: '警告',
                    value: 'warning'
                }
            ],
            typeOption: [
                {
                    label: '线',
                    value: 'line'
                },
                {
                    label: '圆',
                    value: 'circle'
                },
                {
                    label: '仪表',
                    value: 'dashboard'
                }
            ]
        }
    },
    watch: {
        optionModel: {
            immediate: true,
            handler(val) {
                // 如果没有这个字段则添加这个字段
                this.optionModel.LineWidth = val.progressType == 'line' ? 126 : JSON.parse(JSON.stringify(this.optionModel.LineWidth));
                this.optionModel.strokeWidth = val.progressType == 'line' ? JSON.parse(JSON.stringify(this.optionModel.strokeWidth)) : 6;
            },
            deep: true,
        },
    },
    methods: {
        clearStatus() {
            this.optionModel.progressStatus = null;
        },
    },
}
</script>
    
<style scoped></style>
    