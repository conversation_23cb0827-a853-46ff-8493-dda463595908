<template>
  <el-form-item v-if="!optionModel.minHeight && !optionModel.heightFit" :label="selectedWidget.type=='data-table'?'最大高度':'高度'">
    <el-input type="number" v-model="optionModel.height"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "height-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
