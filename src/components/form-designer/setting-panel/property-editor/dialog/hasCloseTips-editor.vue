<template>
<div>
  <el-form-item label="关闭确认">
    <el-switch v-model="optionModel.hasCloseTips" :min="1" class="hide-spin-button" style="width: 100%"></el-switch>
  </el-form-item>
  <el-form-item label="关闭确认内容" v-if="optionModel.hasCloseTips">
    <el-input v-model="optionModel.hasCloseTipsContent" :min="1" class="hide-spin-button" style="width: 100%"></el-input>
  </el-form-item>
</div>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'

export default {
  name: 'hasCloseTips-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>

</style>
