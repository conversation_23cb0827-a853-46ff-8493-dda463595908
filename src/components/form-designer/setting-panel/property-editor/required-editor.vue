<template>
  <!--  :label="i18nt('designer.setting.required')" -->
  <el-form-item>
    <span slot="label">
      {{ i18nt('designer.setting.required') }}
      <el-tooltip v-if="['sub-form','trends-tab'].includes(selectedWidget.type)" effect="light" content="子表单和动态表单的必填项为一行不能全是空">
        <i class="el-icon-info" />
      </el-tooltip>
    </span>
    <el-switch v-model="optionModel.required" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'

export default {
  name: 'RequiredEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped>

</style>
