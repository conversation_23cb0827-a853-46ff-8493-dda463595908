<template>
  <div>
    <el-form-item label="多选">
      <el-switch
        v-model="optionModel.hasSelection"
        :min="1"
        class="hide-spin-button"
        style="width: 100%"
      ></el-switch>
    </el-form-item>
    <el-form-item label="个数限制" v-if="optionModel.hasSelection && !optionModel.linkAble">
      <template #label>
        个数限制
        <el-tooltip effect="light" content="需要配置主键才会生效,默认为id">
          <i class="el-icon-info" />
        </el-tooltip>
      </template>
      <el-input-number
       :min="0"
        v-model="optionModel.selectionLimit"
        style="width: 100px"
      />
    </el-form-item>
    <el-form-item v-if="optionModel.hasSelection" label="选择事件">
      <el-button type="primary" icon="el-icon-edit" plain round @click="WriteCode">
        编写代码</el-button
      >
    </el-form-item>
    <el-dialog
      title="选择事件函数"
      :visible.sync="dialogFlag"
      v-if="dialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="d-flex">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <el-alert
            type="info"
            :closable="false"
            title="((val) => {"
          ></el-alert>
          <code-editor
            :mode="'javascript'"
            adonly="false"
            v-model="optionModel.onSelection"
          ></code-editor>
          <el-alert type="info" :closable="false" title="}"></el-alert>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFlag = false">
          {{ i18nt('designer.hint.confirm') }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index.vue';
import componentTree from '../components/componentTree.vue';
export default {
  name: 'hasSelection-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
    componentTree,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      dialogFlag: false,
    };
  },
  methods: {
    WriteCode() {
      this.dialogFlag = true;
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },
  },
};
</script>

<style scoped></style>
