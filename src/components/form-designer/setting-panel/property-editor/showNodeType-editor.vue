<template>
  <el-form-item :label="i18nt('designer.setting.showNodeType')">
    <el-select v-model="optionModel.showNodeType" multiple>
      <el-option v-for="item in deptTypeOptions"
                 :key="item.dictCode"
                 :label="item.dictLabel"
                 :value="item.dictCode">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import {getDepartmentType, listDept} from "@/api/system/dept";

  export default {
    name: "showNodeType-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        deptTypeOptions: [
          {label: 'default', value: ''},
          {label: 'large', value: 'large'},
          {label: 'medium', value: 'medium'},
          {label: 'small', value: 'small'},
          {label: 'mini', value: 'mini'},
        ],
      }
    },
    created() {
      getDepartmentType({ dictType: 'sys_dept_type' }).then(response => {
        this.deptTypeOptions = response.rows
      })
    }
  }
</script>

<style scoped>

</style>
