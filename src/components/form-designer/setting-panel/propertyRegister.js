import Vue from 'vue';

/**
 * 格式说明：属性名称==对应属性编辑器的组件名称
 */
const COMMON_PROPERTIES = {
  // 字段
  name: 'name-editor',
  label: 'label-editor',
  title: 'title-editor',
  alias: 'alias-editor',
  tabType: 'tabType-editor',
  tabPosition: 'tabPosition-editor',
  stretch: 'stretch-editor',
  busType: 'busType-editor',
  flowType: 'flowType-editor',
  organizationalList: 'organizational-list-editor',
  showNodeType: 'showNodeType-editor',
  addAble: 'addAble-editor',
  authority: 'authority-editor',
  showCheckAll: 'showCheckAll-editor',
  deleteAble: 'deleteAble-editor',
  rowEdit: 'rowEdit-editor',
  indexes: 'indexes-editor',
  labelAlign: 'labelAlign-editor',
  type: 'type-editor',
  defaultValue: 'defaultValue-editor',
  placeholder: 'placeholder-editor',
  startPlaceholder: 'startPlaceholder-editor',
  endPlaceholder: 'endPlaceholder-editor',
  size: 'size-editor',
  steps: 'steps-editor',
  mark: 'mark-editor',
  showStops: 'showStops-editor',
  showIndex: 'showIndex-editor',
  displayStyle: 'displayStyle-editor',
  buttonStyle: 'buttonStyle-editor',
  border: 'border-editor',
  codeManage: 'codeManage-editor',
  subtitle: 'subtitle-editor',
  subFormCols: 'subFormCols-editor',
  isLazy: 'isLazy-editor',
  isCustom: 'isCustom-editor',
  closeOnClickModal: 'closeOnClickModal-editor',
  destroyOnClose: 'destroyOnClose-editor',
  fullscreen: 'fullscreen-editor',
  showClose: 'showClose-editor',
  hasFrame: 'hasFrame-editor',
  hasExpand: 'hasExpand-editor',
  tableStripe: 'stripe-editor',
  alreadyClickLable: 'alreadyClickLable-editor',
  hasRole: 'hasRole-editor',
  hasPrint: 'hasPrint-editor',
  hasExport: 'hasExport-editor',
  isShowTableField: 'isShowTableField-editor',
  labelWidth: 'labelWidth-editor',
  labelHidden: 'labelHidden-editor',
  rows: 'rows-editor',
  required: 'required-editor',
  validation: 'validation-editor',
  validationHint: 'validationHint-editor',
  readonly: 'readonly-editor',
  disabled: 'disabled-editor',
  direction: 'direction-editor',
  hidden: 'hidden-editor',
  append: 'append-editor',
  prepend: 'prepend-editor',
  showSummary: 'showSummary-editor',
  pageStyle: 'pageStyle-editor',
  pagerCount: 'pagerCount-editor',
  tableShow: 'tableShow-editor',
  dialogTable: 'dialogTable-editor',
  clearable: 'clearable-editor',
  editable: 'editable-editor',
  showPassword: 'showPassword-editor',
  textContent: 'textContent-editor',
  htmlContent: 'htmlContent-editor',
  format: 'format-editor',
  valueFormat: 'valueFormat-editor',
  filterable: 'filterable-editor',
  fit: 'fit-editor',
  allowCreate: 'allowCreate-editor',
  remote: 'remote-editor',
  automaticDropdown: 'automaticDropdown-editor',
  multiple: 'multiple-editor',
  multipleLimit: 'multipleLimit-editor',
  isUser: 'isUser-editor',
  autoRefresh: 'autoRefresh-editor',
  defaultRange: 'defaultRange-editor',
  isEnable: 'isEnable-editor',
  contentPosition: 'contentPosition-editor',
  optionItems: 'optionItems-editor',
  uploadURL: 'uploadURL-editor',
  isShowTip: 'isShowTip-editor',
  showFileList: 'showFileList-editor',
  withCredentials: 'withCredentials-editor',
  multipleSelect: 'multipleSelect-editor',
  limit: 'limit-editor',
  fileMaxSize: 'fileMaxSize-editor',
  fileTypes: 'fileTypes-editor',
  customClass: 'customClass-editor',
  listCustomClass: 'listCustomClass-editor',

  targetOrder: 'targetOrder-editor',
  checkStrictly: 'checkStrictly-editor',
  rowKey: 'rowKey-editor',
  value: 'value-editor',
  showAllLevels: 'showAllLevels-editor',
  selectLabel: 'selectLabel-editor',
  selectValue: 'selectValue-editor',
  selectChildren: 'selectChildren-editor',
  samePage: 'samePage-editor',
  groupPage: 'groupPage-editor',
  bandDataIdsApi: 'bandDataIdsApi-editor',
  defaultTime: 'defaultTime-editor',
  height: 'height-editor',
  minHeight: 'minHeight-editor',
  colWidth: 'colWidth-editor',
  width: 'width-editor',
  writeWidth: 'writeWidth-editor',
  collapseTag: 'collapseTag-editor',
  dialogWidth: 'dialogWidth-editor',
  borderRadius: 'borderRadius-editor',
  backgroundColor: 'backgroundColor-editor',
  nodeKey: 'nodeKey-editor',
  // 容器
  showBlankRow: 'showBlankRow-editor',
  showRowNumber: 'showRowNumber-editor',
  cellWidth: 'cellWidth-editor',
  cellHeight: 'cellHeight-editor',
  gutter: 'gutter-editor',
  cols: 'cols-editor',
  btns: 'btns-editor',
  leftBtns: 'leftBtns-editor',
  color: 'color-editor',
  bgColor: 'bgColor-editor',
  labelColor: 'labelColor-editor',
  upload: 'upload-editor',
  operation: 'operation-editor',
  responsive: 'responsive-editor',
  span: 'span-editor',
  offset: 'offset-editor',
  push: 'push-editor',
  pull: 'pull-editor',
  popupSelect: 'popupSelect-editor',
  interval: 'interval-editor',
  drawerDirection: 'drawer-direction-editor',
  autoplay: 'autoplay-editor',
  textarea: 'textarea-editor',
  heightFit: 'heightFit-editor',
  isCard: 'isCard-editor',
  activities: 'activities-editor',
  reverse: 'reverse-editor',
  collapse: 'collapse-editor',
  accordion: 'accordion-editor',
  slot: 'slot-editor',
  showFold: 'fold-editor',
  showCheckbox: 'showCheckbox-editor',
  emptyText: 'emptyText-editor',
  indent: 'indent-editor',
  iconClass: 'iconClass-editor',
  hasSearch: 'hasSearch-editor',
  hasCloseTips: 'hasCloseTips-editor',
  margin: 'margin-editor',
  padding: 'padding-editor',
  border2: 'border2-editor',
  reportRelevance: 'report-relevance-editor',
  isShowExport: 'isShowExport-editor',
  isShowPrint: 'isShowPrint-editor',
  isShowQuery: 'isShowQuery-editor',
  barCode: 'barCode-editor',
  bandKey: 'bandKey-editor',
  onlineRelevance: 'online-relevance-editor',
  fileUploadType: 'fileUploadType-editor',
  expandAll: 'expandAll-editor',
  isDrag: 'isDrag-editor',
  numberMode: 'numberMode-editor',
  theme: 'theme-editor',
  min: 'min-editor',
  max: 'max-editor',
  precision: 'precision-editor',
  step: 'step-editor',
  controlsPosition: 'controlsPosition-editor',
  minLength: 'minLength-editor',
  maxLength: 'maxLength-editor',
  showWordLimit: 'showWordLimit-editor',
  prefixIcon: 'prefixIcon-editor',
  suffixIcon: 'suffixIcon-editor',
  switchWidth: 'switchWidth-editor',
  activeText: 'activeText-editor',
  inactiveText: 'inactiveText-editor',
  activeColor: 'activeColor-editor',
  inactiveColor: 'inactiveColor-editor',
  lowThreshold: 'lowThreshold-editor',
  highThreshold: 'highThreshold-editor',
  allowHalf: 'allowHalf-editor',
  showText: 'showText-editor',
  showScore: 'showScore-editor',
  range: 'range-editor',
  vertical: 'vertical-editor',
  plain: 'plain-editor',
  round: 'round-editor',
  circle: 'circle-editor',
  icon: 'icon-editor',
  labelIconClass: 'labelIconClass-editor',
  labelIconPosition: 'labelIconPosition-editor',
  labelTooltip: 'labelTooltip-editor',
  appendButton: 'appendButton-editor',
  appendButtonDisabled: 'appendButtonDisabled-editor',
  highlightCurrentRow: 'highlightCurrentRow-editor',
  treeTable: 'treeTable-editor',
  hasSelection: 'hasSelection-editor',
  progressType: 'progress-type',
  dropItems: 'dropItems-editor',
  showReply: 'showReply-editor',
};

const NETWORK_PROPERTIES = {
  api: 'api-editor',
  paramConfig: 'paramConfig-editor',
};

const EVENT_PROPERTIES = {
  // 字段
  onCreated: 'onCreated-editor',
  onMounted: 'onMounted-editor',
  onClick: 'onClick-editor',
  onInput: 'onInput-editor',
  onChange: 'onChange-editor',
  allowCreateFn: 'allowCreateFn-editor',
  tabChange: 'tabChange-editor',
  onFocus: 'onFocus-editor',
  onBlur: 'onBlur-editor',
  onRemoteQuery: 'onRemoteQuery-editor',
  onBeforeUpload: 'onBeforeUpload-editor',
  onUploadSuccess: 'onUploadSuccess-editor',
  onUploadError: 'onUploadError-editor',
  onValidate: 'onValidate-editor',
  onDisabledDate: 'onDisabledDate-editor',
  onDialogClose: 'onDialogClose-editor',
  onNodeClick: 'onNodeClick-editor',
  onNodeCheck: 'onNodeCheck-editor',
  onFilterNodeMethod: 'onFilterNodeMethod-editor',
  onSubSave: 'onSubSave-editor',
  onFormCommit: 'onFormCommit-editor',
  onClickPrint: 'onClickPrint-editor',
  onClickExport: 'onClickExport-editor',
  onPrependClick: 'onPrependClick-editor',
  onAppendClick: 'onAppendClick-editor',
  onCellDblclick: 'onCellDblclick-editor',
  onExpandChange: 'onExpandChange-editor',
  onEditChange: 'onEditChange-editor',
  onFocusChange: 'onFocusChange-editor',
  onBlurChange: 'onBlurChange-editor',
  onHandleEmit: 'onHandleEmit-editor',
  onMessage: 'onMessage-editor',
  // 容器
  onSubFormRowAdd: 'onSubFormRowAdd-editor',
  onSubFormRowInsert: 'onSubFormRowInsert-editor',
  onSubFormRowDelete: 'onSubFormRowDelete-editor',
  onSubFormRowChange: 'onSubFormRowChange-editor',
  onRowStyle: 'onRowStyle-editor',
  onLazy: 'onLazy-editor',
  onCellStyle: 'onCellStyle-editor',
  onSpanMethod: 'onSpanMethod-editor',
  onRowClick: 'onRowClick-editor',
  onRowClass: 'onRowClass-editor',
  onCellClick: 'onCellClick-editor',
  onQuery: 'onQuery-editor',
  onSearchClick: 'onSearchClick-editor',
  onResetClick: 'onResetClick-editor',

  onRenderContent: 'onRenderContent-editor',
  onSortChange: 'onSortChange-editor',
  onImageClick: 'onImageClick-editor',
  onScrollEnd: 'onScrollEnd-editor',
  onNodeStyle: 'onNodeStyle-editor',
  onValueClick: 'onValueClick-editor',
  onBeforeDestroy: 'onBeforeDestroy-editor',
  onCommand: 'onCommand-editor',
  onProgressFormat: 'onProgressFormat-editor',
};

/**
 * 注册组件常见属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerCommonProperty(uniquePropName, propEditorName) {
  COMMON_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册组件高级属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerAdvancedProperty(uniquePropName, propEditorName) {
  NETWORK_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册组件事件属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerEventProperty(uniquePropName, propEditorName) {
  EVENT_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册常见属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerCPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerCommonProperty(uniquePropName, propEditorName);
}

/**
 * 注册高级属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerAPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerAdvancedProperty(uniquePropName, propEditorName);
}

/**
 * 注册事件属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerEPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerEventProperty(uniquePropName, propEditorName);
}

export default {
  COMMON_PROPERTIES,
  NETWORK_PROPERTIES,
  EVENT_PROPERTIES,
};
