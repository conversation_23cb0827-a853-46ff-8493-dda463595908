<template>
  <el-container class="panel-container">
    <el-scrollbar class="setting-scrollbar" :style="{ height: scrollerHeight }">
      <el-tabs
        :active-name="activeTab"
        style="height: 100%; width: 100%; overflow: hidden"
      >
        <el-tab-pane :label="i18nt('designer.hint.widgetSetting')" name="1">
          <template
            v-if="
              !!designer.selectedWidget && !designer.selectedWidget.category
            "
          >
            <el-form
              :model="optionModel"
              size="mini"
              label-position="left"
              label-width="120px"
              class="setting-form"
              @submit.native.prevent
            >
              <el-collapse
                v-model="widgetActiveCollapseNames"
                class="setting-collapse"
              >
                <!-- 常见属性 -->
                <el-collapse-item
                  name="1"
                  :title="i18nt('designer.setting.commonSetting')"
                >
                  <template v-for="(editorName, propName) in commonProps">
                    <component
                      :is="getPropEditor(propName, editorName)"
                      :key="editorName"
                      v-if="hasPropEditor(propName, editorName)"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>
                <!-- 网络属性 -->
                <el-collapse-item
                  name="2"
                  :title="i18nt('designer.setting.interfaceSetting')"
                  v-if="Object.entries(netProps).some(item=>hasPropEditor(item[0],item[1]))"
                >
                  <template v-for="(editorName, propName) in netProps">
                    <component
                      :key="editorName"
                      :is="getPropEditor(propName, editorName)"
                      v-if="hasPropEditor(propName, editorName)"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>
                <!-- 事件属性 -->
                <el-collapse-item
                  v-if="Object.entries(eventProps).some(item=>hasPropEditor(item[0],item[1]))"
                  name="3"
                  :title="i18nt('designer.setting.eventSetting')"
                >
                  <template v-for="(editorName, propName) in eventProps">
                    <component
                      :is="getPropEditor(propName, editorName)"
                      v-if="hasPropEditor(propName, editorName)"
                      :key="editorName"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </template>

          <template
            v-if="
              !!designer.selectedWidget && !!designer.selectedWidget.category
            "
          >
            <el-form
              :model="optionModel"
              size="mini"
              label-position="left"
              label-width="120px"
              class="setting-form"
              @submit.native.prevent
            >
              <el-collapse
                v-model="widgetActiveCollapseNames"
                class="setting-collapse"
              >
                <el-collapse-item
                  name="1"
                  :title="i18nt('designer.setting.commonSetting')"
                >
                  <template v-for="(editorName, propName) in commonProps">
                    <component
                      :is="getPropEditor(propName, editorName)"
                      v-if="hasPropEditor(propName, editorName)"
                      :key="editorName"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>

                <el-collapse-item
                  v-if="Object.entries(netProps).some(item=>hasPropEditor(item[0],item[1]))"
                  name="2"
                  :title="i18nt('designer.setting.interfaceSetting')"
                >
                  <template v-for="(editorName, propName) in netProps">
                    <component
                      :is="getPropEditor(propName, editorName)"
                      v-if="hasPropEditor(propName, editorName)"
                      :key="editorName"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>

                <el-collapse-item
                  name="3"
                  :title="i18nt('designer.setting.eventSetting')"
                  v-if="Object.entries(eventProps).some(item=>hasPropEditor(item[0],item[1]))"
                >
                  <template v-for="(editorName, propName) in eventProps">
                    <component
                      :is="getPropEditor(propName, editorName)"
                      v-if="hasPropEditor(propName, editorName)"
                      :key="editorName"
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    />
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </template>
        </el-tab-pane>

        <el-tab-pane
          v-if="!!designer"
          :label="i18nt('designer.hint.formSetting')"
          name="2"
        >

            <form-setting
              :designer="designer"
              :form-config="formConfig"
              :str="str"
            />

        </el-tab-pane>
      </el-tabs>
    </el-scrollbar>

    <!-- 开发文档   -->
    <el-dialog
      title="开发文档"
      :visible.sync="showEventDocumentDialogFlag"
      v-if="showEventDocumentDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      width="80vw"
      v-dialog-drag
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <pre v-html="msg" style="height: 80vh; overflow: auto" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      v-if="showWidgetEventDialogFlag"
      v-dialog-drag
      :title="i18nt('designer.setting.editWidgetEventHandler')"
      :visible.sync="showWidgetEventDialogFlag"
      :show-close="true"
      width="80vw"
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="d-flex">
        <componentTree ref="componentTreeRef" />
        <div class="flex-1">
          <el-alert type="info" :closable="false" :title="eventHeader" />
          <code-editor
            v-model="eventHandlerCode"
            :mode="'javascript'"
            :readonly="false"
          />
          <el-alert type="info" :closable="false" title="}" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="showEventDocumentDialogFlag = true">
          {{ i18nt("designer.hint.document") }}</el-button
        >
        <el-button @click="showWidgetEventDialogFlag = false">
          {{ i18nt("designer.hint.cancel") }}</el-button
        >
        <el-button type="primary" @click="saveEventHandler">
          {{ i18nt("designer.hint.confirm") }}</el-button
        >
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
import CodeEditor from "@/components/code-editor/index.vue";
import PropertyEditors from "./property-editor/index";
import FormSetting from "./form-setting";
import WidgetProperties from "./propertyRegister";
import { addWindowResizeHandler } from "@/utils/util";
import i18n from "@/utils/i18n";
import componentTree from "./components/componentTree.vue";

const { COMMON_PROPERTIES, NETWORK_PROPERTIES, EVENT_PROPERTIES } =
  WidgetProperties;

export default {
  name: "SettingPanel",
  componentName: "SettingPanel",
  mixins: [i18n],
  components: {
    CodeEditor,
    FormSetting,
    ...PropertyEditors,
    componentTree,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    formConfig: Object,
  },
  data() {
    return {
      msg: `


####  发送http请求
\`\`\`javascript
this.getRequest({
  url: '/interfaces/*****',
  method: 'get/post/delete/put',
  data: {
    name: 'xxxxx'
  },
  params: {
    name: '12312312'
  }
}).then(res => {
  console.log(res)
}).catch(err => {
  console.log(err)
})

下载文件
this.download(url, {}, 'order.xlsx')

\`\`\`
#### 消息通知
\`\`\`javascript
this.$notify({title: '成功',message: '这是一条成功的提示消息',type: 'success'});
\`\`\`
#### 消息提示
\`\`\`javascript
this.$message({message: "提示内容", type:"success"}) 系统弹窗消息提示
\`\`\`

#### 打开流程提交界面 busKye 业务id   flag 是否提交，result 关闭返回值
\`\`\`javascript
this.$formModal.start(busKye, (flag, result) => {
  console.log(flag, result)
})
\`\`\`
#### 打开流程审批界面 busKye业务id 、curr_task_id 任务id 、definition_id 实例Id  、select   0审批1查看  、回调

this.$formModal.show(busKye, curr_task_id, definition_id, select, (flag, result) => {
  console.log(flag, result)
})
#### 删除流程
this.$delFlow(definitionId).then(()=>{})
#### 撤回流程
this.$withdrawal(definitionId).then(()=>{})

### 弹框确认
this.$confirm('提示消息', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(res=>{

        }).catch(err=>{

        })

### 获取当前登录人信息
this.$store.state.user   包括 {name,nickName,userId,token,name,dept,avatar}
      `,
      scrollerHeight: 0,
      str: "",
      activeTab: "2",
      widgetActiveCollapseNames: ["1", "2", "3"], // ['1', '2', '3'],
      formActiveCollapseNames: ["1", "2"],

      commonProps: COMMON_PROPERTIES,
      netProps: NETWORK_PROPERTIES,
      eventProps: EVENT_PROPERTIES,

      showWidgetEventDialogFlag: false,
      showEventDocumentDialogFlag: false,
      eventHandlerCode: "",
      curEventName: "",
      eventHeader: "",
    };
  },
  computed: {
    optionModel: {
      get() {
        return this.selectedWidget.options;
      },

      set(newValue) {
        this.selectedWidget.options = newValue;
      },
    },
  },
  watch: {
    "designer.selectedWidget": {
      handler(val) {
        if (val) {
          this.activeTab = "1";
        }
      },
    },
    "designer.widgetList": {
      deep: true,
      immediate: true,
      handler(old, n) {
        if (n) {
          this.setIds(n);
        }
      },
    },

    "selectedWidget.options": {
      // 组件属性变动后，立即保存表单JSON！！
      deep: true,
      handler() {
        this.designer.saveCurrentHistoryStep();
      },
    },
    formConfig: {
      deep: true,
      handler() {
        this.designer.saveCurrentHistoryStep();
      },
    },
  },
  created() {
    this.$on("editEventHandler", function (eventName, eventParams) {
      this.editEventHandler(eventName, eventParams);
    });

    this.designer.handleEvent("form-css-updated", (cssClassList) => {
      this.designer.setCssClassList(cssClassList);
    });
  },
  mounted() {
    if (!this.designer.selectedWidget) {
      this.activeTab = "2";
    } else {
      this.activeTab = "1";
    }

    this.scrollerHeight = window.innerHeight - 150 + "px";
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight = window.innerHeight - 150 + "px";
      });
    });
    this.setIds(this.designer.widgetList);
  },
  methods: {
    setIds(n) {
      function fun(arr) {
        arr.forEach((item) => {
          if (item.options.label && item.options.name) {
            str += `
                 ${item.options.label} : ${item.options.name}`;
          }
          if (item.widgetList && item.widgetList.length > 0) {
            fun(item.widgetList);
          }
        });
      }
      let str = ``;
      fun(n);
      this.str = str;
    },
    hasPropEditor(propName, editorName) {
      if (!editorName) {
        return false;
      }
      let originalPropName = propName.replace(
        this.selectedWidget.type + "-",
        ""
      ); // 去掉组件名称前缀-，如果有的话！！
      return this.designer.hasConfig(this.selectedWidget, originalPropName);
    },

    getPropEditor(propName, editorName) {
      let originalPropName = propName.replace(
        this.selectedWidget.type + "-",
        ""
      ); // 去掉组件名称前缀-，如果有的话！！
      let ownPropEditorName = `${this.selectedWidget.type}-${originalPropName}-editor`;
      if (this.$options.components[ownPropEditorName]) {
        // 局部注册的属性编辑器组件
        return ownPropEditorName;
      }

      return this.$root.$options.components[ownPropEditorName]
        ? ownPropEditorName
        : editorName; // 全局注册的属性编辑器组件
    },

    editEventHandler(eventName, eventParams) {
      this.curEventName = eventName;
      this.eventHeader = `${
        this.optionModel.name
      }.${eventName}(${eventParams.join(", ")}) {`;
      this.eventHandlerCode = this.selectedWidget.options[eventName] || "";

      // 设置字段校验函数示例代码
      if (eventName === "onValidate" && !this.optionModel["onValidate"]) {
        this.eventHandlerCode =
          "  /* sample code */\n  /*\n  if ((value > 100) || (value <script 0)) {\n    callback(new Error('error message'))  //fail\n  } else {\n    callback();  //pass\n  }\n  */";
      }

      this.showWidgetEventDialogFlag = true;
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },

    saveEventHandler() {
      this.selectedWidget.options[this.curEventName] = this.eventHandlerCode;
      this.showWidgetEventDialogFlag = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__header {
  margin-bottom: 0;
}
::v-deep .el-tabs__content {
  padding: 0 5px;
}
::v-deep .el-tabs__nav{
  margin-left: 10px;
}

.setting-scrollbar {
 width: 100%;
}

.setting-collapse {
  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
  }

  ::v-deep .el-collapse-item__header {
    font-style: italic;
    font-weight: bold;
  }
}

.setting-form {
  ::v-deep .el-form-item__label {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 6px;
  }
}

/* 隐藏Chrome浏览器中el-input数字输入框右侧的上下调整小箭头 */
::v-deep .hide-spin-button input::-webkit-outer-spin-button,
::v-deep .hide-spin-button input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/* 隐藏Firefox浏览器中el-input数字输入框右侧的上下调整小箭头 */
::v-deep .hide-spin-button input[type="number"] {
  -moz-appearance: textfield;
}

::v-deep .custom-divider.el-divider--horizontal {
  margin: 10px 0;
}

::v-deep .custom-divider-margin-top.el-divider--horizontal {
  margin: 20px 0;
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 0 15px;
  }
}

</style>
