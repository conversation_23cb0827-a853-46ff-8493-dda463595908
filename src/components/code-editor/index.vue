<template>
  <div class="ace-container" style="position:relative">
    <el-tooltip  content="代码格式化(Ctrl+Shift+F)">
      <svg-icon icon-class="code" style="position:absolute;top:5px;right:5px;z-index:9999;cursor:pointer" @click.native="format"></svg-icon>
    </el-tooltip>
    
    <!-- 官方文档中使用id，这里禁止使用，在后期打包后容易出现问题，使用 ref 或者 DOM 就行 -->
    <div class="ace-editor" ref="ace" @keydown.ctrl.shift.70="format">
      
    </div>
  </div>
</template>

<script>
import ace from "ace-builds";
/* 启用此行后webpack打包回生成很多动态加载的js文件，不便于部署，故禁用！！
     特别提示：禁用此行后，需要调用ace.config.set('basePath', 'path...')指定动态js加载URL！！
   */
//import 'ace-builds/webpack-resolver'

// import 'ace-builds/src-min-noconflict/theme-monokai' // 默认设置的主题
import "ace-builds/src-min-noconflict/theme-sqlserver"; // 新设主题
import "ace-builds/src-min-noconflict/mode-javascript"; // 默认设置的语言模式
import "ace-builds/src-min-noconflict/mode-json"; //
import "ace-builds/src-min-noconflict/mode-css"; //
import "ace-builds/src-min-noconflict/mode-sql"; //
import "ace-builds/src-min-noconflict/ext-language_tools";
import { ACE_BASE_PATH } from "@/utils/config";
import beautify from "js-beautify";
export default {
  name: "CodeEditor",
  props: {
    value: {
      type: String,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "javascript",
    },
    userWorker: {
      //是否开启语法检查，默认开启
      type: Boolean,
      default: true,
    },
    maxLines: {
      type: Number,
      default: 36,
    },
  },
  mounted() {
    //ace.config.set('basePath', 'https://ks3-cn-beijing.ksyun.com/vform2021/ace')
    ace.config.set("basePath", ACE_BASE_PATH);

    this.addAutoCompletion(ace); //添加自定义代码提示！！

    this.aceEditor = ace.edit(this.$refs.ace, {
      maxLines: this.maxLines, // 最大行数，超过会自动出现滚动条
      minLines: 5, // 最小行数，还未到最大行数时，编辑器会自动伸缩大小
      fontSize: 12, // 编辑器内字体大小
      theme: this.themePath, // 默认设置的主题
      mode: this.modePath, // 默认设置的语言模式
      tabSize: 2, // 制表符设置为2个空格大小
      readOnly: this.readonly,
      highlightActiveLine: true,
      value: this.codeValue,
    });

    this.aceEditor.setOptions({
      enableBasicAutocompletion: true,
      enableSnippets: true, // 设置代码片段提示
      enableLiveAutocompletion: true, // 设置自动提示
      enableBehaviours: true,
    });


    if (this.mode === "json") {
      this.setJsonMode();
    } else if (this.mode === "css") {
      this.setCssMode();
    }

    if (!this.userWorker) {
      this.aceEditor.getSession().setUseWorker(false);
    }

    //编辑时同步数据
    this.aceEditor.getSession().on("change", (ev) => {
      //this.$emit('update:value', this.aceEditor.getValue())  // 触发更新事件, 实现.sync双向绑定！！
      this.$emit("input", this.aceEditor.getValue());
    });
  },
  data() {
    return {
      aceEditor: null,
      themePath: "ace/theme/sqlserver", // 不导入 webpack-resolver，该模块路径会报错
      modePath: `ace/mode/${this.mode}`, // 同上
      codeValue: this.value,
      ids: "11",
    };
  },
  methods: {
    format() {
      let code = beautify(this.aceEditor.getValue(), {
        indent_size: 2, //缩进两个空格
        space_in_empty_paren: true,
      });
      this.aceEditor.setValue(code);
    },
    addAutoCompletion(ace) {
      let acData = [
        {
          meta: "获取指定组件Vue对象",
          caption: "getWidgetRef",
          value: "getWidgetRef()",
          score: 2,
        },
        {
          meta: "获取指定组件ref对象",
          caption: "getFormRef",
          value: "getFormRef()",
          score: 2,
        },
        {
          meta: "数据表格刷新数据",
          caption: "updateRefresh",
          value: "updateRefresh()",
          score: 1,
        },
        {
          meta: "获取组件值",
          caption: "getValue",
          value: "getValue()",
          score: 1,
        },
        {
          meta: "获取组件值",
          caption: "$getValue",
          value: "$getValue()",
          score: 1,
        },
        {
          meta: "设置组件值",
          caption: "$setValue",
          value: "$setValue()",
          score: 1,
        },
        {
          meta: "设置组件值(不会触发onChange)",
          caption: "setValue",
          value: "setValue()",
          score: 1,
        },
        {
          meta: "改变组件值(触发onChange)",
          caption: "changeValue",
          value: "changeValue()",
          score: 1,
        },
        {
          meta: "发送http请求",
          caption: "getRequest",
          value: `
this.getRequest({
  url: '/interfaces/*****',
  method: 'get/post/delete/put',
  data: {
    name: 'xxxxx'
  },
  params: {
    name: '12312312'
  }
}).then(res => {
  console.log(res)
}).catch(err => {
  console.log(err)
})
          `,
          score: 1,
        },
        {
          meta: "初始化图表数据",
          caption: "initChart",
          value: "initChart()",
          score: 3,
        },
        {
          meta: "设置弹窗是否显示",
          caption: "setDialogVisible",
          value: "setDialogVisible()",
          score: 1,
        },
        {
          meta: "消息提示",
          caption: "message",
          value: '$message({message: "提示内容", type:"success"})',
          score: 1,
        },
        {
          meta: "获取循环组件当前列的下标和列表数据",
          caption: "message",
          value: "getParentIndexItem()",
          score: 1,
        },
        {
          meta: "获取组织树当前点击的节点",
          caption: "getClickNode",
          value: "getClickNode()",
          score: 1,
        },
        {
          meta: "获取组织树当前选中的节点",
          caption: "getCheckNodes",
          value: "getCheckNodes()",
          score: 1,
        },
        { meta: "打印", caption: "console", value: "console.log()", score: 1 },
        //TODO: 待补充！！
      ];
      let langTools = ace.require("ace/ext/language_tools");
      langTools.addCompleter({
        getCompletions: function (editor, session, pos, prefix, callback) {
          if (prefix.length === 0) {
            return callback(null, []);
          } else {
            return callback(null, acData);
          }
        },
      });
    },

    setJsonMode() {
      this.aceEditor.getSession().setMode("ace/mode/json");
    },

    setCssMode() {
      this.aceEditor.getSession().setMode("ace/mode/css");
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .ace-editor {
//   height: 504px !important;
// }
.ace-editor {
  min-height: 500px;
}
</style>
