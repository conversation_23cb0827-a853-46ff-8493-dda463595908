import request from '@/utils/request'
// 查询树配置列表
export function getTreeList(query) {
  return request({
    url: '/system/tree/list',
    method: 'get',
    params: query
  })
}

export function getLogList(params) {
  return request({
    url: '/system/apiLog/pageList',
    method: 'get',
    params
  })
}

export function getApiLog(id) {
  return request({
    url: `/system/apiLog/getApiLog/${id}`,
    method: 'get'
  })
}

/**
 * 保存备份数据
 * @param {*} data
 * @returns
 */
export function saveBackupData(data) {
  return request({
    url: '/flows/backupData/save',
    method: 'post',
    data
  })
}

export function getBackupDataList(params) {
  return request({
    url: '/flows/backupData/pageList',
    method: 'get',
    params
  })
}

export function getBackupDataDetail(id) {
  return request({
    url: `/flows/backupData/getBackupData/${id}`,
    method: 'get'
  })
}