<template>
  <VFormDesigner v-if="testJson" :in-designer="testJson" ref="vForm" />
</template>

<script>
import VFormDesigner from '@/components/form-designer/index.vue';
import { getInfo } from '@/api/tool/form';
import { createDesigner } from '@/components/form-designer/designer';
import { deepClone } from '@/utils/util';
export default {
  name: 'index',
  components: {
    VFormDesigner,
  },
  props: {
    formId: null,
  },
  data() {
    return {
      testJson: null,
    };
  },
  provide() {
    return {
      formObj: {
        formId: this.formId,
      },
    };
  },
  beforeRouteLeave(to, from, next) {
    this.$confirm('即将离开页面, 是否保存?', '警告', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then((res) => {
        this.$refs.vForm.$refs.toolbal.generateSave();
                this.$store.dispatch("tagsView/delView", this.$route);
        next();
      })
      .catch((action) => {
        if (action == 'cancel') {
          this.$store.dispatch("tagsView/delView", this.$route);
          next();
        }
      });
  },
  created() {
    let formIdInt =
      parseInt(this.$route.params.formId) || parseInt(this.formId);
    getInfo(formIdInt, { design: true }).then((res) => {
      if (res.data.templateJson) {
        this.testJson = JSON.parse(res.data.templateJson);
      } else {
        let designer = createDesigner(this);
        let widgetList = deepClone(designer.widgetList);
        let formConfig = deepClone(designer.formConfig);
        this.testJson = JSON.parse(
          JSON.stringify({ widgetList, formConfig }, null, '  '),
        );
      }
    });
  },
};
</script>

<style scoped>
#app {
  height: 100%;
}
</style>
