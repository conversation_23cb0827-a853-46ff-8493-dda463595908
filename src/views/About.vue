<template>
  <div class="about">
    <h1>关于页面</h1>
    <p>这是关于页面的内容</p>
    <p>页面访问量: {{ pageViews }}</p>
  </div>
</template>

<script>
export default {
  name: 'About',
  data() {
    return {
      pageViews: 1234,
      components: 8,
      routes: 3
    }
  },
  mounted() {
    // 模拟页面访问量增加
    this.incrementPageViews()
  },
  methods: {
    incrementPageViews() {
      this.pageViews += Math.floor(Math.random() * 10) + 1
    }
  }
}
</script>

<style scoped>
.about {
  padding: 40px 20px;
  min-height: 80vh;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

.info-section h2 {
  color: #409eff;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.info-section p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 1.5rem;
}

.tech-list, .feature-list {
  list-style: none;
  padding: 0;
}

.tech-list li, .feature-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #ebeef5;
  color: #606266;
}

.tech-list li:last-child, .feature-list li:last-child {
  border-bottom: none;
}

.stats {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-item {
  text-align: center;
  margin-bottom: 2rem;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-item h3 {
  font-size: 2rem;
  color: #409eff;
  margin-bottom: 0.5rem;
}

.stat-item p {
  color: #909399;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  h1 {
    font-size: 2rem;
  }
}
</style>
