<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎来到首页</h1>
      <p>这是一个使用Vue 2 + Vite + Vue Router构建的测试应用</p>
      <div class="features">
        <div class="feature-card">
          <h3>🚀 快速开发</h3>
          <p>使用Vite提供的快速热重载功能</p>
        </div>
        <div class="feature-card">
          <h3>🎯 Vue Router</h3>
          <p>单页面应用路由管理</p>
        </div>
        <div class="feature-card">
          <h3>💡 Element UI</h3>
          <p>丰富的UI组件库</p>
        </div>
      </div>
      <div class="actions">
        <router-link to="/about" class="btn btn-primary">了解更多</router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      message: '欢迎使用Vue Router!'
    }
  }
}
</script>

<style scoped>
.home {
  padding: 40px 20px;
  text-align: center;
}

.hero {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 3rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #7f8c8d;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-primary:hover {
  background: #337ecc;
}

.btn-secondary {
  background: #67c23a;
  color: white;
}

.btn-secondary:hover {
  background: #529b2e;
}
</style>
