# @ 别名配置测试

## 配置说明

已在 `vite.config.js` 中配置了路径别名：

```javascript
resolve: {
    alias: {
        '@': resolve(__dirname, 'src')
    }
}
```

## 使用示例

### 1. 在路由配置中使用
```javascript
// src/router/index.js
import Home from '@/views/Home.vue'
import About from '@/views/About.vue'
import Contact from '@/views/Contact.vue'
```

### 2. 在主入口文件中使用
```javascript
// src/main.js
import App from '@/App.vue'
import router from '@/router'
```

### 3. 在组件中使用
```javascript
// 导入其他组件
import SomeComponent from '@/components/SomeComponent.vue'

// 导入工具函数
import { someUtil } from '@/utils/helpers.js'

// 导入API
import api from '@/api/index.js'
```

## 测试方法

1. 检查开发服务器是否正常启动
2. 查看浏览器控制台是否有模块解析错误
3. 验证路由跳转是否正常工作
4. 确认热重载功能正常

## 优势

- **简化导入路径**: 不需要使用相对路径 `../../../`
- **提高可维护性**: 文件移动时不需要修改导入路径
- **增强可读性**: 路径更清晰明了
- **IDE支持**: 大多数IDE都支持路径别名的智能提示

## 注意事项

- 确保在TypeScript项目中也配置相应的路径映射
- 某些工具可能需要额外配置才能识别别名
- 在测试环境中可能需要单独配置别名解析
