# Vue 2 + Vite + Vue Router 测试项目

## 项目概述

这是一个基于Vue 2.7、Vite和Vue Router构建的单页面应用(SPA)测试项目。项目展示了如何在Vue 2项目中集成路由功能，并创建多个页面进行导航。

## 技术栈

- **Vue 2.7.16**: 渐进式JavaScript框架
- **Vue Router 3.6.5**: Vue.js官方路由管理器
- **Vite 7.0.4**: 下一代前端构建工具
- **Element UI 2.15.14**: 基于Vue 2的桌面端组件库
- **pnpm**: 快速、节省磁盘空间的包管理器

## 项目结构

```
vue2-vite/
├── src/
│   ├── components/          # 组件目录
│   ├── views/              # 页面组件
│   │   ├── Home.vue        # 首页
│   │   ├── About.vue       # 关于页面
│   │   └── Contact.vue     # 联系页面
│   ├── router/             # 路由配置
│   │   └── index.js        # 路由配置文件
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── docs/                   # 文档目录
│   └── README.md           # 项目文档
└── package.json            # 项目配置
```

## 功能特性

### 🚀 路由功能
- ✅ 单页面应用路由管理
- ✅ 历史模式路由(History Mode)
- ✅ 路由导航守卫
- ✅ 404页面重定向

### 🎨 页面设计
- ✅ 响应式布局设计
- ✅ 现代化UI界面
- ✅ 导航栏和页脚
- ✅ 移动端适配

### 📱 页面功能
- **首页**: 项目介绍和功能展示
- **关于页面**: 技术栈介绍和项目统计
- **联系页面**: 联系信息和消息表单

## 安装和运行

### 环境要求
- Node.js >= 14
- pnpm >= 6

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 预览生产版本
```bash
pnpm preview
```

## 路由配置

项目使用Vue Router 3进行路由管理，配置文件位于`src/router/index.js`：

```javascript
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/contact',
    name: 'Contact',
    component: Contact
  },
  {
    path: '*',
    redirect: '/'
  }
]
```

## 测试用例

### 路由测试
1. **首页访问**: 访问 `http://localhost:5173/` 应显示首页内容
2. **关于页面**: 点击导航栏"关于"或访问 `/about` 应显示关于页面
3. **联系页面**: 点击导航栏"联系"或访问 `/contact` 应显示联系页面
4. **404处理**: 访问不存在的路径应重定向到首页

### 功能测试
1. **导航功能**: 导航栏链接应正确跳转到对应页面
2. **活动状态**: 当前页面的导航链接应显示活动状态
3. **响应式**: 在不同屏幕尺寸下页面应正常显示
4. **表单功能**: 联系页面的表单应能正常提交(模拟)

### 性能测试
1. **热重载**: 修改代码后页面应自动刷新
2. **构建速度**: 使用Vite构建应比传统webpack更快
3. **页面加载**: 页面切换应流畅无卡顿

## 开发说明

### 添加新页面
1. 在`src/views/`目录下创建新的Vue组件
2. 在`src/router/index.js`中添加路由配置
3. 在`src/App.vue`的导航栏中添加链接

### 样式规范
- 使用scoped样式避免样式冲突
- 采用响应式设计，支持移动端
- 使用CSS Grid和Flexbox进行布局

### 组件规范
- 组件名使用PascalCase命名
- 文件名使用kebab-case命名
- 每个组件应包含name属性

## 常见问题

### Q: 为什么使用Vue Router 3而不是4？
A: Vue Router 4是为Vue 3设计的，Vue 2项目应使用Vue Router 3。

### Q: 如何配置路由的base路径？
A: 在router配置中设置`base`属性，例如：`base: '/my-app/'`

### Q: 如何实现路由懒加载？
A: 使用动态import：`component: () => import('../views/About.vue')`

## 更新日志

### v1.1.0 (2024-08-03)
- ✅ 配置@别名映射到src目录
- ✅ 更新所有导入路径使用@别名
- ✅ 修复Contact.vue文件缺失问题
- ✅ 优化开发体验

### v1.0.0 (2024-08-03)
- ✅ 初始化项目结构
- ✅ 集成Vue Router
- ✅ 创建首页、关于、联系页面
- ✅ 实现响应式导航栏
- ✅ 添加项目文档

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。
