import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue2";
import commonjs from "vite-plugin-commonjs";
import Components from "unplugin-vue-components/vite";
import { resolve } from "path";

export default defineConfig({
    plugins: [
        vue(),
        commonjs(),
        Components({})
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src')
        }
    },
    server: {
        host: '0.0.0.0', // 允许外部访问
        port: 5173,      // 指定端口
        strictPort: false, // 如果端口被占用，自动尝试下一个可用端口
        open: false      // 不自动打开浏览器
    }
})
